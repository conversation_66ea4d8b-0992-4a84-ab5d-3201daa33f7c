package com.weihai.remotecontrol.ui.dialog

import android.app.Dialog
import android.os.Bundle
import android.widget.EditText
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import com.weihai.remotecontrol.R

/**
 * 编辑分组对话框
 */
class EditGroupDialog : DialogFragment() {
    
    private var originalName: String = ""
    private var onSaveCallback: ((String) -> Unit)? = null
    
    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val editText = EditText(requireContext()).apply {
            hint = getString(R.string.group_name)
            setText(originalName)
            selectAll()
            setPadding(48, 32, 48, 32)
        }
        
        return AlertDialog.Builder(requireContext())
            .setTitle(R.string.edit)
            .setView(editText)
            .setPositiveButton(R.string.save) { _, _ ->
                val groupName = editText.text.toString().trim()
                if (groupName.isNotBlank()) {
                    onSaveCallback?.invoke(groupName)
                }
            }
            .setNegativeButton(R.string.cancel, null)
            .create()
    }
    
    companion object {
        fun show(fragmentManager: FragmentManager, originalName: String, onSave: (String) -> Unit) {
            val dialog = EditGroupDialog()
            dialog.originalName = originalName
            dialog.onSaveCallback = onSave
            dialog.show(fragmentManager, "EditGroupDialog")
        }
    }
}
