<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/gray_50"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:padding="16dp">

    <!-- 分组图标和信息 -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="horizontal">

        <ImageView
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginEnd="12dp"
            android:background="@drawable/circle_primary_bg"
            android:padding="8dp"
            android:src="@drawable/ic_folder"
            android:tint="@color/primary" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvGroupName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/dark"
                android:textSize="16sp"
                android:textStyle="bold"
                tools:text="经区" />

            <TextView
                android:id="@+id/tvDeviceCount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/gray_500"
                android:textSize="14sp"
                tools:text="共 1 个设备" />

        </LinearLayout>

    </LinearLayout>

    <!-- 设备类型统计 -->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="24dp"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvIndoorCount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/indoor_text"
                android:textSize="18sp"
                android:textStyle="bold"
                tools:text="0" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/device_type_indoor"
                android:textColor="@color/gray_500"
                android:textSize="12sp" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvStandardCount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/standard_text"
                android:textSize="18sp"
                android:textStyle="bold"
                tools:text="1" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/device_type_standard"
                android:textColor="@color/gray_500"
                android:textSize="12sp" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
