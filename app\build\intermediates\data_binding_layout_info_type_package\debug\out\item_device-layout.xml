<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_device" modulePackage="com.weihai.remotecontrol" filePath="app\src\main\res\layout\item_device.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_device_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="177" endOffset="51"/></Target><Target id="@+id/tvDeviceName" view="TextView"><Expressions/><location startLine="28" startOffset="12" endLine="38" endOffset="41"/></Target><Target id="@+id/chipGroup" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="40" startOffset="12" endLine="47" endOffset="33"/></Target><Target id="@+id/chipType" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="49" startOffset="12" endLine="54" endOffset="33"/></Target><Target id="@+id/tvDeviceCode" view="TextView"><Expressions/><location startLine="82" startOffset="16" endLine="90" endOffset="43"/></Target><Target id="@+id/btnCopyCode" view="ImageButton"><Expressions/><location startLine="92" startOffset="16" endLine="99" endOffset="51"/></Target><Target id="@+id/tvDevicePassword" view="TextView"><Expressions/><location startLine="126" startOffset="16" endLine="134" endOffset="41"/></Target><Target id="@+id/btnCopyPassword" view="ImageButton"><Expressions/><location startLine="136" startOffset="16" endLine="143" endOffset="51"/></Target><Target id="@+id/btnEditDevice" view="ImageButton"><Expressions/><location startLine="154" startOffset="16" endLine="161" endOffset="51"/></Target><Target id="@+id/btnDeleteDevice" view="ImageButton"><Expressions/><location startLine="163" startOffset="16" endLine="169" endOffset="50"/></Target></Targets></Layout>