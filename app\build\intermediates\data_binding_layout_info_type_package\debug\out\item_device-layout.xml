<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_device" modulePackage="com.weihai.remotecontrol" filePath="app\src\main\res\layout\item_device.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_device_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="165" endOffset="51"/></Target><Target id="@+id/tvDeviceName" view="TextView"><Expressions/><location startLine="27" startOffset="12" endLine="35" endOffset="33"/></Target><Target id="@+id/chipGroup" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="42" startOffset="16" endLine="49" endOffset="38"/></Target><Target id="@+id/chipType" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="51" startOffset="16" endLine="57" endOffset="37"/></Target><Target id="@+id/tvDeviceCode" view="TextView"><Expressions/><location startLine="78" startOffset="12" endLine="85" endOffset="39"/></Target><Target id="@+id/btnCopyCode" view="ImageButton"><Expressions/><location startLine="87" startOffset="12" endLine="93" endOffset="47"/></Target><Target id="@+id/tvDevicePassword" view="TextView"><Expressions/><location startLine="112" startOffset="12" endLine="119" endOffset="37"/></Target><Target id="@+id/btnCopyPassword" view="ImageButton"><Expressions/><location startLine="121" startOffset="12" endLine="127" endOffset="47"/></Target><Target id="@+id/btnEditDevice" view="ImageButton"><Expressions/><location startLine="144" startOffset="12" endLine="151" endOffset="47"/></Target><Target id="@+id/btnDeleteDevice" view="ImageButton"><Expressions/><location startLine="153" startOffset="12" endLine="159" endOffset="46"/></Target></Targets></Layout>