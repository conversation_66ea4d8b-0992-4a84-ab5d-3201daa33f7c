# 远程控制码管理中心 - Android版

这是一个基于原HTML应用还原的原生Android项目，用于管理远程控制设备的连接码和密码。

## 功能特性

### 🎯 核心功能
- **设备管理**：添加、编辑、删除设备信息
- **分组管理**：创建、编辑、删除设备分组
- **搜索功能**：支持设备名称、连接码、密码的模糊搜索
- **数据统计**：查看设备和分组的统计信息
- **数据持久化**：使用SharedPreferences本地存储

### 📱 界面特性
- **Material Design 3**：现代化的UI设计
- **响应式布局**：适配不同屏幕尺寸
- **双列网格**：设备卡片以2列形式展示
- **底部导航**：设备管理和统计页面切换
- **搜索实时过滤**：输入即时显示搜索结果

### 🏷️ 设备类型
- **标准设备**：绿色标签显示
- **室内设备**：蓝色标签显示

### 📊 统计功能
- 总设备数量统计
- 分组数量统计
- 设备类型分布统计
- 各分组详细设备统计

## 项目结构

```
app/
├── src/main/
│   ├── java/com/weihai/remotecontrol/
│   │   ├── data/                    # 数据层
│   │   │   ├── Device.kt           # 设备数据模型
│   │   │   └── DataRepository.kt   # 数据仓库
│   │   ├── ui/
│   │   │   ├── adapter/            # RecyclerView适配器
│   │   │   ├── dialog/             # 对话框
│   │   │   ├── fragment/           # Fragment
│   │   │   └── viewmodel/          # ViewModel
│   │   └── MainActivity.kt         # 主Activity
│   └── res/
│       ├── layout/                 # 布局文件
│       ├── values/                 # 资源文件
│       ├── drawable/               # 图标资源
│       └── menu/                   # 菜单资源
```

## 技术栈

- **语言**：Kotlin
- **架构**：MVVM + Repository Pattern
- **UI框架**：Material Design 3
- **数据存储**：SharedPreferences + Gson
- **异步处理**：LiveData + ViewModel
- **导航**：ViewPager2 + BottomNavigationView

## 初始数据

应用首次启动时会自动初始化26个设备数据，按威海市行政区划分组：

### 分组分布
- **经区**：1个设备
- **环翠区**：15个设备  
- **文登区**：2个设备
- **乳山市**：2个设备
- **荣成市**：3个设备
- **高新区**：1个设备

### 示例设备
- 石岛 (荣成市) - 连接码: 53559193, 密码: 968510
- 水务集团 (环翠区) - 连接码: 12064843, 密码: 763370
- 凤林 (经区) - 连接码: 96788760, 密码: 691581
- ...等26个设备

## 主要功能说明

### 设备管理
1. **添加设备**：点击顶部"新增设备"按钮
2. **编辑设备**：点击设备卡片上的编辑按钮
3. **删除设备**：点击设备卡片上的删除按钮
4. **复制信息**：点击连接码或密码旁的复制按钮

### 分组管理
1. **添加分组**：点击顶部"新增分组"按钮
2. **编辑分组**：双击分组选项卡
3. **删除分组**：长按分组选项卡拖拽到删除区域
4. **调整顺序**：长按分组选项卡左右拖拽

### 搜索功能
- 在顶部搜索框输入关键词
- 支持搜索设备名称、连接码、密码、分组名称
- 实时显示搜索结果
- 分组选项卡显示搜索结果数量

### 统计页面
- 查看总设备数、分组数量
- 查看室内设备和标准设备数量
- 查看各分组的详细设备统计

## 编译和运行

1. 使用Android Studio打开项目
2. 确保Android SDK版本 >= 24 (Android 7.0)
3. 编译目标SDK版本：34 (Android 14)
4. 点击运行按钮或使用命令：`./gradlew assembleDebug`

## 权限说明

- **VIBRATE**：长按操作时的触觉反馈
- **INTERNET**：预留网络功能（当前版本未使用）

## 版本信息

- **版本号**：1.0
- **最低Android版本**：7.0 (API 24)
- **目标Android版本**：14 (API 34)
- **编译工具版本**：Android Gradle Plugin 8.2.0
- **Kotlin版本**：1.9.10

## 特色功能

### 🎨 UI/UX设计
- 采用Material Design 3设计规范
- 支持浅色主题
- 流畅的动画效果
- 直观的操作反馈

### 📱 交互体验
- 一键复制连接码和密码
- 智能搜索和过滤
- 拖拽操作支持
- 触觉反馈

### 💾 数据管理
- 本地数据持久化
- 自动数据备份
- 智能分组管理
- 实时统计更新

这个Android应用完全还原了HTML版本的所有功能，并针对移动端进行了优化，提供了更好的用户体验和性能表现。
