1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.weihai.remotecontrol"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.VIBRATE" />
11-->D:\Software\weihai\android\app\src\main\AndroidManifest.xml:5:5-66
11-->D:\Software\weihai\android\app\src\main\AndroidManifest.xml:5:22-63
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\Software\weihai\android\app\src\main\AndroidManifest.xml:6:5-67
12-->D:\Software\weihai\android\app\src\main\AndroidManifest.xml:6:22-64
13
14    <permission
14-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5490b645e9d6a61333b77d70f2a2a530\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
15        android:name="com.weihai.remotecontrol.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
15-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5490b645e9d6a61333b77d70f2a2a530\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
16        android:protectionLevel="signature" />
16-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5490b645e9d6a61333b77d70f2a2a530\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
17
18    <uses-permission android:name="com.weihai.remotecontrol.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
18-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5490b645e9d6a61333b77d70f2a2a530\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
18-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5490b645e9d6a61333b77d70f2a2a530\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
19
20    <application
20-->D:\Software\weihai\android\app\src\main\AndroidManifest.xml:8:5-29:19
21        android:allowBackup="true"
21-->D:\Software\weihai\android\app\src\main\AndroidManifest.xml:9:9-35
22        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
22-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\5490b645e9d6a61333b77d70f2a2a530\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
23        android:dataExtractionRules="@xml/data_extraction_rules"
23-->D:\Software\weihai\android\app\src\main\AndroidManifest.xml:10:9-65
24        android:debuggable="true"
25        android:extractNativeLibs="false"
26        android:fullBackupContent="@xml/backup_rules"
26-->D:\Software\weihai\android\app\src\main\AndroidManifest.xml:11:9-54
27        android:icon="@mipmap/ic_launcher"
27-->D:\Software\weihai\android\app\src\main\AndroidManifest.xml:12:9-43
28        android:label="@string/app_name"
28-->D:\Software\weihai\android\app\src\main\AndroidManifest.xml:13:9-41
29        android:roundIcon="@mipmap/ic_launcher_round"
29-->D:\Software\weihai\android\app\src\main\AndroidManifest.xml:14:9-54
30        android:supportsRtl="true"
30-->D:\Software\weihai\android\app\src\main\AndroidManifest.xml:15:9-35
31        android:testOnly="true"
32        android:theme="@style/Theme.RemoteControl" >
32-->D:\Software\weihai\android\app\src\main\AndroidManifest.xml:16:9-51
33        <activity
33-->D:\Software\weihai\android\app\src\main\AndroidManifest.xml:19:9-27:20
34            android:name="com.weihai.remotecontrol.MainActivity"
34-->D:\Software\weihai\android\app\src\main\AndroidManifest.xml:20:13-41
35            android:exported="true"
35-->D:\Software\weihai\android\app\src\main\AndroidManifest.xml:21:13-36
36            android:theme="@style/Theme.RemoteControl" >
36-->D:\Software\weihai\android\app\src\main\AndroidManifest.xml:22:13-55
37            <intent-filter>
37-->D:\Software\weihai\android\app\src\main\AndroidManifest.xml:23:13-26:29
38                <action android:name="android.intent.action.MAIN" />
38-->D:\Software\weihai\android\app\src\main\AndroidManifest.xml:24:17-69
38-->D:\Software\weihai\android\app\src\main\AndroidManifest.xml:24:25-66
39
40                <category android:name="android.intent.category.LAUNCHER" />
40-->D:\Software\weihai\android\app\src\main\AndroidManifest.xml:25:17-77
40-->D:\Software\weihai\android\app\src\main\AndroidManifest.xml:25:27-74
41            </intent-filter>
42        </activity>
43
44        <provider
44-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\cef2556a3e4f69125db25bc898eaad5d\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
45            android:name="androidx.startup.InitializationProvider"
45-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\cef2556a3e4f69125db25bc898eaad5d\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
46            android:authorities="com.weihai.remotecontrol.androidx-startup"
46-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\cef2556a3e4f69125db25bc898eaad5d\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
47            android:exported="false" >
47-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\cef2556a3e4f69125db25bc898eaad5d\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
48            <meta-data
48-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\cef2556a3e4f69125db25bc898eaad5d\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
49                android:name="androidx.emoji2.text.EmojiCompatInitializer"
49-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\cef2556a3e4f69125db25bc898eaad5d\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
50                android:value="androidx.startup" />
50-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\cef2556a3e4f69125db25bc898eaad5d\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
51            <meta-data
51-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e920ffd14558d116a290e3519cdc2c1\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
52                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
52-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e920ffd14558d116a290e3519cdc2c1\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
53                android:value="androidx.startup" />
53-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e920ffd14558d116a290e3519cdc2c1\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
54            <meta-data
54-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b5fb7cd0f771a365fe040bc04b82cf3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
55                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
55-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b5fb7cd0f771a365fe040bc04b82cf3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
56                android:value="androidx.startup" />
56-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b5fb7cd0f771a365fe040bc04b82cf3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
57        </provider>
58
59        <uses-library
59-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a425037ec51ceb36230797e634bc297d\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
60            android:name="androidx.window.extensions"
60-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a425037ec51ceb36230797e634bc297d\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
61            android:required="false" />
61-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a425037ec51ceb36230797e634bc297d\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
62        <uses-library
62-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a425037ec51ceb36230797e634bc297d\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
63            android:name="androidx.window.sidecar"
63-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a425037ec51ceb36230797e634bc297d\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
64            android:required="false" />
64-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a425037ec51ceb36230797e634bc297d\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
65
66        <receiver
66-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b5fb7cd0f771a365fe040bc04b82cf3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
67            android:name="androidx.profileinstaller.ProfileInstallReceiver"
67-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b5fb7cd0f771a365fe040bc04b82cf3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
68            android:directBootAware="false"
68-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b5fb7cd0f771a365fe040bc04b82cf3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
69            android:enabled="true"
69-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b5fb7cd0f771a365fe040bc04b82cf3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
70            android:exported="true"
70-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b5fb7cd0f771a365fe040bc04b82cf3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
71            android:permission="android.permission.DUMP" >
71-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b5fb7cd0f771a365fe040bc04b82cf3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
72            <intent-filter>
72-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b5fb7cd0f771a365fe040bc04b82cf3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
73                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
73-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b5fb7cd0f771a365fe040bc04b82cf3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
73-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b5fb7cd0f771a365fe040bc04b82cf3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
74            </intent-filter>
75            <intent-filter>
75-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b5fb7cd0f771a365fe040bc04b82cf3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
76                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
76-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b5fb7cd0f771a365fe040bc04b82cf3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
76-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b5fb7cd0f771a365fe040bc04b82cf3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
77            </intent-filter>
78            <intent-filter>
78-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b5fb7cd0f771a365fe040bc04b82cf3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
79                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
79-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b5fb7cd0f771a365fe040bc04b82cf3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
79-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b5fb7cd0f771a365fe040bc04b82cf3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
80            </intent-filter>
81            <intent-filter>
81-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b5fb7cd0f771a365fe040bc04b82cf3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
82                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
82-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b5fb7cd0f771a365fe040bc04b82cf3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
82-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b5fb7cd0f771a365fe040bc04b82cf3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
83            </intent-filter>
84        </receiver>
85    </application>
86
87</manifest>
