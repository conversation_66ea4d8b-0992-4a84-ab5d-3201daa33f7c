// Generated by view binder compiler. Do not edit!
package com.weihai.remotecontrol.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.chip.Chip;
import com.google.android.material.chip.ChipGroup;
import com.weihai.remotecontrol.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentDevicesBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Chip chipAllDevices;

  @NonNull
  public final ChipGroup chipGroupTabs;

  @NonNull
  public final LinearLayout layoutEmpty;

  @NonNull
  public final RecyclerView recyclerViewDevices;

  @NonNull
  public final TextView tvEmptyMessage;

  @NonNull
  public final TextView tvEmptyTip;

  private FragmentDevicesBinding(@NonNull LinearLayout rootView, @NonNull Chip chipAllDevices,
      @NonNull ChipGroup chipGroupTabs, @NonNull LinearLayout layoutEmpty,
      @NonNull RecyclerView recyclerViewDevices, @NonNull TextView tvEmptyMessage,
      @NonNull TextView tvEmptyTip) {
    this.rootView = rootView;
    this.chipAllDevices = chipAllDevices;
    this.chipGroupTabs = chipGroupTabs;
    this.layoutEmpty = layoutEmpty;
    this.recyclerViewDevices = recyclerViewDevices;
    this.tvEmptyMessage = tvEmptyMessage;
    this.tvEmptyTip = tvEmptyTip;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentDevicesBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentDevicesBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_devices, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentDevicesBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.chipAllDevices;
      Chip chipAllDevices = ViewBindings.findChildViewById(rootView, id);
      if (chipAllDevices == null) {
        break missingId;
      }

      id = R.id.chipGroupTabs;
      ChipGroup chipGroupTabs = ViewBindings.findChildViewById(rootView, id);
      if (chipGroupTabs == null) {
        break missingId;
      }

      id = R.id.layoutEmpty;
      LinearLayout layoutEmpty = ViewBindings.findChildViewById(rootView, id);
      if (layoutEmpty == null) {
        break missingId;
      }

      id = R.id.recyclerViewDevices;
      RecyclerView recyclerViewDevices = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewDevices == null) {
        break missingId;
      }

      id = R.id.tvEmptyMessage;
      TextView tvEmptyMessage = ViewBindings.findChildViewById(rootView, id);
      if (tvEmptyMessage == null) {
        break missingId;
      }

      id = R.id.tvEmptyTip;
      TextView tvEmptyTip = ViewBindings.findChildViewById(rootView, id);
      if (tvEmptyTip == null) {
        break missingId;
      }

      return new FragmentDevicesBinding((LinearLayout) rootView, chipAllDevices, chipGroupTabs,
          layoutEmpty, recyclerViewDevices, tvEmptyMessage, tvEmptyTip);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
