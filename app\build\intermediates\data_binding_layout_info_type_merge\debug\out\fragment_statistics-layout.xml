<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_statistics" modulePackage="com.weihai.remotecontrol" filePath="app\src\main\res\layout\fragment_statistics.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_statistics_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="227" endOffset="12"/></Target><Target id="@+id/tvTotalDevices" view="TextView"><Expressions/><location startLine="64" startOffset="20" endLine="71" endOffset="41"/></Target><Target id="@+id/tvTotalGroups" view="TextView"><Expressions/><location startLine="102" startOffset="20" endLine="109" endOffset="40"/></Target><Target id="@+id/tvIndoorDevices" view="TextView"><Expressions/><location startLine="140" startOffset="20" endLine="147" endOffset="40"/></Target><Target id="@+id/tvStandardDevices" view="TextView"><Expressions/><location startLine="178" startOffset="20" endLine="185" endOffset="41"/></Target><Target id="@+id/recyclerViewGroupStats" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="215" startOffset="16" endLine="219" endOffset="44"/></Target></Targets></Layout>