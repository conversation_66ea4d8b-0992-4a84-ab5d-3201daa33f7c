{"logs": [{"outputFile": "com.weihai.remotecontrol.app-mergeDebugResources-47:/values-ko/values-ko.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a9838fd12d930687e6917e413d05bcf9\\transformed\\material-1.11.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,249,314,378,447,521,600,683,789,864,926,1007,1069,1126,1213,1273,1331,1389,1448,1505,1559,1654,1710,1767,1821,1887,1991,2066,2143,2264,2329,2394,2494,2573,2648,2698,2749,2815,2879,2949,3026,3097,3165,3236,3303,3373,3466,3546,3620,3700,3782,3854,3919,3991,4039,4112,4176,4251,4328,4390,4454,4517,4584,4668,4746,4826,4904,4958,5013", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,64,63,68,73,78,82,105,74,61,80,61,56,86,59,57,57,58,56,53,94,55,56,53,65,103,74,76,120,64,64,99,78,74,49,50,65,63,69,76,70,67,70,66,69,92,79,73,79,81,71,64,71,47,72,63,74,76,61,63,62,66,83,77,79,77,53,54,71", "endOffsets": "244,309,373,442,516,595,678,784,859,921,1002,1064,1121,1208,1268,1326,1384,1443,1500,1554,1649,1705,1762,1816,1882,1986,2061,2138,2259,2324,2389,2489,2568,2643,2693,2744,2810,2874,2944,3021,3092,3160,3231,3298,3368,3461,3541,3615,3695,3777,3849,3914,3986,4034,4107,4171,4246,4323,4385,4449,4512,4579,4663,4741,4821,4899,4953,5008,5080"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2879,2944,3008,3077,3151,3907,3990,4096,4171,4233,4314,4376,4433,4520,4580,4638,4696,4755,4812,4866,4961,5017,5074,5128,5194,5298,5373,5450,5571,5636,5701,5801,5880,5955,6005,6056,6122,6186,6256,6333,6404,6472,6543,6610,6680,6773,6853,6927,7007,7089,7161,7226,7298,7346,7419,7483,7558,7635,7697,7761,7824,7891,7975,8053,8133,8211,8265,8522", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,109", "endColumns": "12,64,63,68,73,78,82,105,74,61,80,61,56,86,59,57,57,58,56,53,94,55,56,53,65,103,74,76,120,64,64,99,78,74,49,50,65,63,69,76,70,67,70,66,69,92,79,73,79,81,71,64,71,47,72,63,74,76,61,63,62,66,83,77,79,77,53,54,71", "endOffsets": "294,2939,3003,3072,3146,3225,3985,4091,4166,4228,4309,4371,4428,4515,4575,4633,4691,4750,4807,4861,4956,5012,5069,5123,5189,5293,5368,5445,5566,5631,5696,5796,5875,5950,6000,6051,6117,6181,6251,6328,6399,6467,6538,6605,6675,6768,6848,6922,7002,7084,7156,7221,7293,7341,7414,7478,7553,7630,7692,7756,7819,7886,7970,8048,8128,8206,8260,8315,8589"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5490b645e9d6a61333b77d70f2a2a530\\transformed\\core-1.12.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,438,534,632,732", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "142,242,336,433,529,627,727,828"}, "to": {"startLines": "38,39,40,41,42,43,44,111", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3230,3322,3422,3516,3613,3709,3807,8673", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "3317,3417,3511,3608,3704,3802,3902,8769"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1ae08651db4bbb07f10e1945a5f6d854\\transformed\\appcompat-1.6.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,296,397,479,577,683,763,838,929,1022,1117,1211,1311,1404,1499,1593,1684,1775,1855,1953,2047,2142,2242,2339,2439,2591,2685", "endColumns": "96,93,100,81,97,105,79,74,90,92,94,93,99,92,94,93,90,90,79,97,93,94,99,96,99,151,93,78", "endOffsets": "197,291,392,474,572,678,758,833,924,1017,1112,1206,1306,1399,1494,1588,1679,1770,1850,1948,2042,2137,2237,2334,2434,2586,2680,2759"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "299,396,490,591,673,771,877,957,1032,1123,1216,1311,1405,1505,1598,1693,1787,1878,1969,2049,2147,2241,2336,2436,2533,2633,2785,8594", "endColumns": "96,93,100,81,97,105,79,74,90,92,94,93,99,92,94,93,90,90,79,97,93,94,99,96,99,151,93,78", "endOffsets": "391,485,586,668,766,872,952,1027,1118,1211,1306,1400,1500,1593,1688,1782,1873,1964,2044,2142,2236,2331,2431,2528,2628,2780,2874,8668"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\822ef5295c54b3fc6575f1874f116e7d\\transformed\\navigation-ui-2.7.6\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,155", "endColumns": "99,101", "endOffsets": "150,252"}, "to": {"startLines": "107,108", "startColumns": "4,4", "startOffsets": "8320,8420", "endColumns": "99,101", "endOffsets": "8415,8517"}}]}]}