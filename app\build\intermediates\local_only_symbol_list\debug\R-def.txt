R_DEF: Internal format may change without notice
local
color black
color bottom_nav_color
color danger
color dark
color delete_zone_bg
color delete_zone_border
color delete_zone_text
color gray_100
color gray_200
color gray_300
color gray_400
color gray_50
color gray_500
color gray_600
color gray_700
color gray_800
color gray_900
color indoor_bg
color indoor_text
color info
color light
color primary
color primary_variant
color secondary
color secondary_variant
color standard_bg
color standard_text
color success
color transparent
color warning
color white
drawable circle_primary_bg
drawable ic_add
drawable ic_all_devices
drawable ic_clear
drawable ic_copy
drawable ic_delete
drawable ic_devices
drawable ic_devices_large
drawable ic_edit
drawable ic_folder
drawable ic_folder_add
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_launcher_legacy
drawable ic_search
drawable ic_server
drawable ic_statistics
id actvDeviceGroup
id actvDeviceType
id appBarLayout
id bottomNavigation
id btnAddDevice
id btnAddGroup
id btnCancel
id btnCopyCode
id btnCopyPassword
id btnDeleteDevice
id btnEditDevice
id btnSave
id chipAllDevices
id chipGroup
id chipGroupTabs
id chipType
id etDeviceCode
id etDeviceName
id etDevicePassword
id etSearch
id layoutEmpty
id nav_devices
id nav_statistics
id recyclerViewDevices
id recyclerViewGroupStats
id searchInputLayout
id tilDeviceCode
id tilDeviceGroup
id tilDeviceName
id tilDevicePassword
id tilDeviceType
id tvDeviceCode
id tvDeviceCount
id tvDeviceName
id tvDevicePassword
id tvEmptyMessage
id tvEmptyTip
id tvGroupName
id tvIndoorCount
id tvIndoorDevices
id tvStandardCount
id tvStandardDevices
id tvTotalDevices
id tvTotalGroups
id viewPager
layout activity_main
layout dialog_add_device
layout fragment_devices
layout fragment_statistics
layout item_device
layout item_group_statistic
menu bottom_navigation
mipmap ic_launcher
mipmap ic_launcher_round
string add
string add_device
string add_group
string all_devices
string app_name
string cancel
string confirm_delete
string confirm_delete_device
string confirm_delete_group
string confirm_delete_group_empty
string confirm_delete_group_message
string copy
string copy_success
string create
string data_statistics
string default_group
string delete
string device_code
string device_count
string device_group
string device_management
string device_name
string device_password
string device_type
string device_type_indoor
string device_type_standard
string drag_to_delete
string edit
string group_detail_statistics
string group_name
string group_name_exists
string indoor_devices
string no_devices_in_group
string no_group_data
string no_search_results
string required_field
string save
string search_hint
string search_tip
string standard_devices
string statistics
string statistics_description
string total_devices
string total_groups
style CardStyle
style ChipStyle
style GroupChip
style IndoorChip
style InputStyle
style PrimaryButton
style SecondaryButton
style StandardChip
style Theme.RemoteControl
xml backup_rules
xml data_extraction_rules
