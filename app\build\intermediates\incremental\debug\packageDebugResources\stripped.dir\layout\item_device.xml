<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/CardStyle"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 设备名称和标签行 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvDeviceName"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textColor="@color/dark"
                android:textSize="18sp"
                android:textStyle="bold"
                tools:text="石岛" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <com.google.android.material.chip.Chip
                    android:id="@+id/chipGroup"
                    style="@style/GroupChip"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end"
                    android:layout_marginBottom="4dp"
                    tools:text="荣成市" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chipType"
                    style="@style/StandardChip"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end"
                    tools:text="标准" />

            </LinearLayout>

        </LinearLayout>

        <!-- 连接码行 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:layout_width="60dp"
                android:layout_height="wrap_content"
                android:text="@string/device_code"
                android:textColor="@color/gray_500"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tvDeviceCode"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textColor="@color/dark"
                android:textSize="14sp"
                tools:text="53559193" />

            <ImageButton
                android:id="@+id/btnCopyCode"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_copy"
                android:tint="@color/primary" />

        </LinearLayout>

        <!-- 连接密码行 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:layout_width="60dp"
                android:layout_height="wrap_content"
                android:text="@string/device_password"
                android:textColor="@color/gray_500"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tvDevicePassword"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textColor="@color/dark"
                android:textSize="14sp"
                tools:text="968510" />

            <ImageButton
                android:id="@+id/btnCopyPassword"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_copy"
                android:tint="@color/primary" />

        </LinearLayout>

        <!-- 操作按钮行 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginBottom="8dp"
            android:background="@color/gray_100" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="end"
            android:orientation="horizontal">

            <ImageButton
                android:id="@+id/btnEditDevice"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginEnd="8dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_edit"
                android:tint="@color/warning" />

            <ImageButton
                android:id="@+id/btnDeleteDevice"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_delete"
                android:tint="@color/danger" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
