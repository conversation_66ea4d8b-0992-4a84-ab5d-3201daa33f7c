<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_devices" modulePackage="com.weihai.remotecontrol" filePath="app\src\main\res\layout\fragment_devices.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/fragment_devices_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="81" endOffset="14"/></Target><Target id="@+id/chipGroupTabs" view="com.google.android.material.chip.ChipGroup"><Expressions/><location startLine="15" startOffset="8" endLine="32" endOffset="52"/></Target><Target id="@+id/chipAllDevices" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="23" startOffset="12" endLine="30" endOffset="57"/></Target><Target id="@+id/recyclerViewDevices" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="37" startOffset="4" endLine="42" endOffset="38"/></Target><Target id="@+id/layoutEmpty" view="LinearLayout"><Expressions/><location startLine="45" startOffset="4" endLine="79" endOffset="18"/></Target><Target id="@+id/tvEmptyMessage" view="TextView"><Expressions/><location startLine="61" startOffset="8" endLine="67" endOffset="37"/></Target><Target id="@+id/tvEmptyTip" view="TextView"><Expressions/><location startLine="69" startOffset="8" endLine="77" endOffset="39"/></Target></Targets></Layout>