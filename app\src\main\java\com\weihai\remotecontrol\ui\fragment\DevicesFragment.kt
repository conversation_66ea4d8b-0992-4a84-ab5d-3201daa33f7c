package com.weihai.remotecontrol.ui.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.GridLayoutManager
import com.google.android.material.chip.Chip
import com.weihai.remotecontrol.R
import com.weihai.remotecontrol.data.Device
import com.weihai.remotecontrol.databinding.FragmentDevicesBinding
import com.weihai.remotecontrol.ui.adapter.DeviceAdapter
import com.weihai.remotecontrol.ui.viewmodel.MainViewModel

/**
 * 设备管理Fragment
 */
class DevicesFragment : Fragment() {
    
    private var _binding: FragmentDevicesBinding? = null
    private val binding get() = _binding!!
    
    private val viewModel: MainViewModel by activityViewModels()
    private lateinit var deviceAdapter: DeviceAdapter
    
    private var currentGroup = "all"
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentDevicesBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupRecyclerView()
        setupGroupTabs()
        observeViewModel()
    }
    
    private fun setupRecyclerView() {
        deviceAdapter = DeviceAdapter(
            onEditClick = { device ->
                viewModel.editDevice(device)
            },
            onDeleteClick = { device ->
                viewModel.showDeleteDeviceDialog(device)
            }
        )
        
        binding.recyclerViewDevices.apply {
            adapter = deviceAdapter
            layoutManager = GridLayoutManager(requireContext(), 2)
        }
    }
    
    private fun setupGroupTabs() {
        // 监听分组选择
        binding.chipGroupTabs.setOnCheckedStateChangeListener { group, checkedIds ->
            if (checkedIds.isNotEmpty()) {
                val checkedChip = group.findViewById<Chip>(checkedIds[0])
                val groupName = checkedChip.tag as? String ?: "all"
                currentGroup = groupName
                viewModel.setCurrentGroup(groupName)
            }
        }
    }
    
    private fun observeViewModel() {
        // 观察分组数据
        viewModel.groups.observe(viewLifecycleOwner) { groups ->
            updateGroupTabs(groups)
        }
        
        // 观察设备数据
        viewModel.filteredDevices.observe(viewLifecycleOwner) { devices ->
            updateDeviceList(devices)
        }
        
        // 观察搜索查询
        viewModel.searchQuery.observe(viewLifecycleOwner) { query ->
            updateEmptyState(query)
        }
    }
    
    private fun updateGroupTabs(groups: List<String>) {
        binding.chipGroupTabs.removeAllViews()
        
        // 添加"全部设备"选项卡
        val allChip = createGroupChip("全部设备", "all", true)
        binding.chipGroupTabs.addView(allChip)
        
        // 添加其他分组选项卡
        groups.forEach { group ->
            val chip = createGroupChip(group, group, false)
            binding.chipGroupTabs.addView(chip)
        }
    }
    
    private fun createGroupChip(text: String, tag: String, isChecked: Boolean): Chip {
        return Chip(requireContext()).apply {
            this.text = text
            this.tag = tag
            this.isCheckable = true
            this.isChecked = isChecked
            setChipIconResource(if (tag == "all") R.drawable.ic_all_devices else R.drawable.ic_folder)
        }
    }
    
    private fun updateDeviceList(devices: List<Device>) {
        deviceAdapter.submitList(devices)
        
        // 更新空状态
        val isEmpty = devices.isEmpty()
        binding.recyclerViewDevices.visibility = if (isEmpty) View.GONE else View.VISIBLE
        binding.layoutEmpty.visibility = if (isEmpty) View.VISIBLE else View.GONE
    }
    
    private fun updateEmptyState(searchQuery: String) {
        if (searchQuery.isNotBlank()) {
            binding.tvEmptyMessage.text = getString(R.string.no_search_results, searchQuery)
            binding.tvEmptyTip.visibility = View.VISIBLE
        } else {
            binding.tvEmptyMessage.text = getString(R.string.no_devices_in_group)
            binding.tvEmptyTip.visibility = View.GONE
        }
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
