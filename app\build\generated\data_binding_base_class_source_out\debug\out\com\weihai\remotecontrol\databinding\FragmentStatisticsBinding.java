// Generated by view binder compiler. Do not edit!
package com.weihai.remotecontrol.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.weihai.remotecontrol.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentStatisticsBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final RecyclerView recyclerViewGroupStats;

  @NonNull
  public final TextView tvIndoorDevices;

  @NonNull
  public final TextView tvStandardDevices;

  @NonNull
  public final TextView tvTotalDevices;

  @NonNull
  public final TextView tvTotalGroups;

  private FragmentStatisticsBinding(@NonNull ScrollView rootView,
      @NonNull RecyclerView recyclerViewGroupStats, @NonNull TextView tvIndoorDevices,
      @NonNull TextView tvStandardDevices, @NonNull TextView tvTotalDevices,
      @NonNull TextView tvTotalGroups) {
    this.rootView = rootView;
    this.recyclerViewGroupStats = recyclerViewGroupStats;
    this.tvIndoorDevices = tvIndoorDevices;
    this.tvStandardDevices = tvStandardDevices;
    this.tvTotalDevices = tvTotalDevices;
    this.tvTotalGroups = tvTotalGroups;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentStatisticsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentStatisticsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_statistics, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentStatisticsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.recyclerViewGroupStats;
      RecyclerView recyclerViewGroupStats = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewGroupStats == null) {
        break missingId;
      }

      id = R.id.tvIndoorDevices;
      TextView tvIndoorDevices = ViewBindings.findChildViewById(rootView, id);
      if (tvIndoorDevices == null) {
        break missingId;
      }

      id = R.id.tvStandardDevices;
      TextView tvStandardDevices = ViewBindings.findChildViewById(rootView, id);
      if (tvStandardDevices == null) {
        break missingId;
      }

      id = R.id.tvTotalDevices;
      TextView tvTotalDevices = ViewBindings.findChildViewById(rootView, id);
      if (tvTotalDevices == null) {
        break missingId;
      }

      id = R.id.tvTotalGroups;
      TextView tvTotalGroups = ViewBindings.findChildViewById(rootView, id);
      if (tvTotalGroups == null) {
        break missingId;
      }

      return new FragmentStatisticsBinding((ScrollView) rootView, recyclerViewGroupStats,
          tvIndoorDevices, tvStandardDevices, tvTotalDevices, tvTotalGroups);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
