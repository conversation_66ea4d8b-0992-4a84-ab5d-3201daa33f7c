<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_add_device" modulePackage="com.weihai.remotecontrol" filePath="app\src\main\res\layout\dialog_add_device.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_add_device_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="126" endOffset="14"/></Target><Target id="@+id/tilDeviceName" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="17" startOffset="4" endLine="33" endOffset="59"/></Target><Target id="@+id/etDeviceName" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="26" startOffset="8" endLine="31" endOffset="34"/></Target><Target id="@+id/tilDeviceGroup" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="35" startOffset="4" endLine="49" endOffset="59"/></Target><Target id="@+id/actvDeviceGroup" view="AutoCompleteTextView"><Expressions/><location startLine="43" startOffset="8" endLine="47" endOffset="38"/></Target><Target id="@+id/tilDeviceType" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="51" startOffset="4" endLine="65" endOffset="59"/></Target><Target id="@+id/actvDeviceType" view="AutoCompleteTextView"><Expressions/><location startLine="59" startOffset="8" endLine="63" endOffset="38"/></Target><Target id="@+id/tilDeviceCode" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="67" startOffset="4" endLine="83" endOffset="59"/></Target><Target id="@+id/etDeviceCode" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="76" startOffset="8" endLine="81" endOffset="34"/></Target><Target id="@+id/tilDevicePassword" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="85" startOffset="4" endLine="101" endOffset="59"/></Target><Target id="@+id/etDevicePassword" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="94" startOffset="8" endLine="99" endOffset="34"/></Target><Target id="@+id/btnCancel" view="Button"><Expressions/><location startLine="109" startOffset="8" endLine="115" endOffset="43"/></Target><Target id="@+id/btnSave" view="Button"><Expressions/><location startLine="117" startOffset="8" endLine="122" endOffset="41"/></Target></Targets></Layout>