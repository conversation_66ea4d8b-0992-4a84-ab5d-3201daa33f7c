// Generated by view binder compiler. Do not edit!
package com.weihai.remotecontrol.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.chip.Chip;
import com.weihai.remotecontrol.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemDeviceBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final ImageButton btnCopyCode;

  @NonNull
  public final ImageButton btnCopyPassword;

  @NonNull
  public final ImageButton btnDeleteDevice;

  @NonNull
  public final ImageButton btnEditDevice;

  @NonNull
  public final Chip chipGroup;

  @NonNull
  public final Chip chipType;

  @NonNull
  public final TextView tvDeviceCode;

  @NonNull
  public final TextView tvDeviceName;

  @NonNull
  public final TextView tvDevicePassword;

  private ItemDeviceBinding(@NonNull MaterialCardView rootView, @NonNull ImageButton btnCopyCode,
      @NonNull ImageButton btnCopyPassword, @NonNull ImageButton btnDeleteDevice,
      @NonNull ImageButton btnEditDevice, @NonNull Chip chipGroup, @NonNull Chip chipType,
      @NonNull TextView tvDeviceCode, @NonNull TextView tvDeviceName,
      @NonNull TextView tvDevicePassword) {
    this.rootView = rootView;
    this.btnCopyCode = btnCopyCode;
    this.btnCopyPassword = btnCopyPassword;
    this.btnDeleteDevice = btnDeleteDevice;
    this.btnEditDevice = btnEditDevice;
    this.chipGroup = chipGroup;
    this.chipType = chipType;
    this.tvDeviceCode = tvDeviceCode;
    this.tvDeviceName = tvDeviceName;
    this.tvDevicePassword = tvDevicePassword;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemDeviceBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemDeviceBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_device, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemDeviceBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnCopyCode;
      ImageButton btnCopyCode = ViewBindings.findChildViewById(rootView, id);
      if (btnCopyCode == null) {
        break missingId;
      }

      id = R.id.btnCopyPassword;
      ImageButton btnCopyPassword = ViewBindings.findChildViewById(rootView, id);
      if (btnCopyPassword == null) {
        break missingId;
      }

      id = R.id.btnDeleteDevice;
      ImageButton btnDeleteDevice = ViewBindings.findChildViewById(rootView, id);
      if (btnDeleteDevice == null) {
        break missingId;
      }

      id = R.id.btnEditDevice;
      ImageButton btnEditDevice = ViewBindings.findChildViewById(rootView, id);
      if (btnEditDevice == null) {
        break missingId;
      }

      id = R.id.chipGroup;
      Chip chipGroup = ViewBindings.findChildViewById(rootView, id);
      if (chipGroup == null) {
        break missingId;
      }

      id = R.id.chipType;
      Chip chipType = ViewBindings.findChildViewById(rootView, id);
      if (chipType == null) {
        break missingId;
      }

      id = R.id.tvDeviceCode;
      TextView tvDeviceCode = ViewBindings.findChildViewById(rootView, id);
      if (tvDeviceCode == null) {
        break missingId;
      }

      id = R.id.tvDeviceName;
      TextView tvDeviceName = ViewBindings.findChildViewById(rootView, id);
      if (tvDeviceName == null) {
        break missingId;
      }

      id = R.id.tvDevicePassword;
      TextView tvDevicePassword = ViewBindings.findChildViewById(rootView, id);
      if (tvDevicePassword == null) {
        break missingId;
      }

      return new ItemDeviceBinding((MaterialCardView) rootView, btnCopyCode, btnCopyPassword,
          btnDeleteDevice, btnEditDevice, chipGroup, chipType, tvDeviceCode, tvDeviceName,
          tvDevicePassword);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
