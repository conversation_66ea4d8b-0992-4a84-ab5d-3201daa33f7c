[{"merged": "com.weihai.remotecontrol.app-mergeDebugResources-48:/layout/item_device.xml", "source": "com.weihai.remotecontrol.app-main-51:/layout/item_device.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-mergeDebugResources-48:\\layout\\fragment_statistics.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-main-51:\\layout\\fragment_statistics.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-mergeDebugResources-48:\\layout\\item_device.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-main-51:\\layout\\item_device.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-mergeDebugResources-48:\\layout\\activity_main.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-main-51:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-mergeDebugResources-48:\\layout\\fragment_devices.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-main-51:\\layout\\fragment_devices.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-mergeDebugResources-48:\\layout\\dialog_add_device.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-main-51:\\layout\\dialog_add_device.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-mergeDebugResources-48:\\layout\\item_group_statistic.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-main-51:\\layout\\item_group_statistic.xml"}]