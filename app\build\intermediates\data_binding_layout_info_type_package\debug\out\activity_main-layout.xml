<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.weihai.remotecontrol" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_main_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="116" endOffset="51"/></Target><Target id="@+id/appBarLayout" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="10" startOffset="4" endLine="94" endOffset="53"/></Target><Target id="@+id/btnAddGroup" view="Button"><Expressions/><location startLine="47" startOffset="16" endLine="56" endOffset="45"/></Target><Target id="@+id/btnAddDevice" view="Button"><Expressions/><location startLine="58" startOffset="16" endLine="66" endOffset="45"/></Target><Target id="@+id/searchInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="71" startOffset="12" endLine="90" endOffset="67"/></Target><Target id="@+id/etSearch" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="81" startOffset="16" endLine="88" endOffset="42"/></Target><Target id="@+id/viewPager" view="androidx.viewpager2.widget.ViewPager2"><Expressions/><location startLine="97" startOffset="4" endLine="102" endOffset="65"/></Target><Target id="@+id/bottomNavigation" view="com.google.android.material.bottomnavigation.BottomNavigationView"><Expressions/><location startLine="105" startOffset="4" endLine="114" endOffset="44"/></Target></Targets></Layout>