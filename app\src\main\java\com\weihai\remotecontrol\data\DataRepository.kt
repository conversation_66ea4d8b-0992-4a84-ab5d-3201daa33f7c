package com.weihai.remotecontrol.data

import android.content.Context
import android.content.SharedPreferences
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

/**
 * 数据仓库类，负责数据的存储和管理
 */
class DataRepository private constructor(context: Context) {
    
    private val sharedPreferences: SharedPreferences = 
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    private val gson = Gson()
    
    private var _groups = mutableListOf<String>()
    private var _devices = mutableListOf<Device>()
    
    val groups: List<String> get() = _groups.toList()
    val devices: List<Device> get() = _devices.toList()
    
    init {
        loadData()
        if (_devices.isEmpty()) {
            initializeDefaultData()
        }
    }
    
    /**
     * 加载数据
     */
    private fun loadData() {
        // 加载分组
        val groupsJson = sharedPreferences.getString(KEY_GROUPS, null)
        if (groupsJson != null) {
            val type = object : TypeToken<List<String>>() {}.type
            _groups = gson.fromJson(groupsJson, type) ?: mutableListOf()
        }
        
        // 加载设备
        val devicesJson = sharedPreferences.getString(KEY_DEVICES, null)
        if (devicesJson != null) {
            val type = object : TypeToken<List<Device>>() {}.type
            _devices = gson.fromJson(devicesJson, type) ?: mutableListOf()
        }
        
        // 如果没有分组但有设备，使用默认分组
        if (_groups.isEmpty() && _devices.isNotEmpty()) {
            _groups = mutableListOf("默认分组")
            saveData()
        }
    }
    
    /**
     * 保存数据
     */
    private fun saveData() {
        sharedPreferences.edit().apply {
            putString(KEY_GROUPS, gson.toJson(_groups))
            putString(KEY_DEVICES, gson.toJson(_devices))
            apply()
        }
    }
    
    /**
     * 初始化默认数据
     */
    private fun initializeDefaultData() {
        // 初始化分组
        _groups = mutableListOf("经区", "环翠区", "文登区", "乳山市", "荣成市", "高新区", "临港区")
        
        // 初始化设备数据
        val defaultDevices = listOf(
            Device(Device.generateId(), "经区皂埠村西自动机场", "经区", DeviceType.STANDARD, "60516419", "972745"),
            Device(Device.generateId(), "环翠绿轴大厦", "环翠区", DeviceType.STANDARD, "28158490", "815673"),
            Device(Device.generateId(), "荣成泰正自动机场", "荣成市", DeviceType.STANDARD, "67304528", "610654"),
            Device(Device.generateId(), "荣成水务集团自动机场", "荣成市", DeviceType.STANDARD, "12064843", "763370"),
            Device(Device.generateId(), "环翠张村富甲花园自动机场", "环翠区", DeviceType.STANDARD, "21928254", "233191"),
            Device(Device.generateId(), "环翠海港大厦自动机场", "环翠区", DeviceType.STANDARD, "43608393", "553427"),
            Device(Device.generateId(), "乳山富豪集团自动机场", "乳山市", DeviceType.STANDARD, "77773727", "515632"),
            Device(Device.generateId(), "文登滨海街道自动机场", "文登区", DeviceType.STANDARD, "30059412", "147061"),
            Device(Device.generateId(), "文登营镇政府", "文登区", DeviceType.STANDARD, "81902386", "670270"),
            Device(Device.generateId(), "文登森霖自动机场", "文登区", DeviceType.STANDARD, "92604820", "395412"),
            Device(Device.generateId(), "经区凤林自动机场", "经区", DeviceType.STANDARD, "96788760", "691581"),
            Device(Device.generateId(), "高区中泰汽车自动机场", "高新区", DeviceType.STANDARD, "73861604", "673512"),
            Device(Device.generateId(), "荣成石岛自动机场", "荣成市", DeviceType.STANDARD, "53559193", "968510"),
            Device(Device.generateId(), "文登银河自动机场", "文登区", DeviceType.STANDARD, "87508684", "390984"),
            Device(Device.generateId(), "环翠国际信托自动机场", "环翠区", DeviceType.STANDARD, "97984157", "438743"),
            Device(Device.generateId(), "临港草庙子自动机场", "临港区", DeviceType.STANDARD, "21649261", "659886"),
            Device(Device.generateId(), "文登房产交易厅自动机场", "文登区", DeviceType.STANDARD, "91722096", "959828"),
            Device(Device.generateId(), "环翠孙家疃自动机场", "环翠区", DeviceType.STANDARD, "93227770", "993546"),
            Device(Device.generateId(), "荣成港西纹石宝滩自动机场", "荣成市", DeviceType.STANDARD, "82341997", "969669"),
            Device(Device.generateId(), "高区审批中心自动机场", "高新区", DeviceType.STANDARD, "66080900", "458336"),
            Device(Device.generateId(), "高区九九大厦", "高新区", DeviceType.STANDARD, "74784625", "698642"),
            Device(Device.generateId(), "乳山滕甲庄自动机场", "乳山市", DeviceType.STANDARD, "61010481", "107439"),
            Device(Device.generateId(), "经区皇冠自动机场", "经区", DeviceType.STANDARD, "11922942", "543248"),
            Device(Device.generateId(), "乳山三赖会馆", "乳山市", DeviceType.STANDARD, "40024552", "122062"),
            Device(Device.generateId(), "荣成华资自动机场", "荣成市", DeviceType.STANDARD, "87538337", "419018"),
            Device(Device.generateId(), "环翠温泉镇江家寨自动机场", "环翠区", DeviceType.STANDARD, "65523396", "220611"),
            Device(Device.generateId(), "环翠羊亭自动机场", "环翠区", DeviceType.STANDARD, "74784625", "698642")
        )
        
        _devices = defaultDevices.toMutableList()
        saveData()
    }
    
    // 分组操作
    fun addGroup(name: String): Boolean {
        if (_groups.contains(name)) return false
        _groups.add(name)
        saveData()
        return true
    }
    
    fun editGroup(oldName: String, newName: String): Boolean {
        if (oldName == newName) return true
        if (_groups.contains(newName)) return false
        
        val index = _groups.indexOf(oldName)
        if (index == -1) return false
        
        _groups[index] = newName
        // 更新设备的分组
        _devices.forEach { device ->
            if (device.group == oldName) {
                device.group = newName
            }
        }
        saveData()
        return true
    }
    
    fun deleteGroup(name: String): Boolean {
        val index = _groups.indexOf(name)
        if (index == -1 || index == 0) return false // 不允许删除第一个分组
        
        _groups.removeAt(index)
        // 将使用此分组的设备移动到第一个分组
        val defaultGroup = _groups[0]
        _devices.forEach { device ->
            if (device.group == name) {
                device.group = defaultGroup
            }
        }
        saveData()
        return true
    }
    
    fun reorderGroups(fromIndex: Int, toIndex: Int): Boolean {
        if (fromIndex == toIndex || fromIndex < 0 || toIndex < 0 || 
            fromIndex >= _groups.size || toIndex >= _groups.size) {
            return false
        }
        
        val movedGroup = _groups.removeAt(fromIndex)
        _groups.add(toIndex, movedGroup)
        saveData()
        return true
    }
    
    // 设备操作
    fun getDevices(group: String? = null): List<Device> {
        return if (group == null || group == "all") {
            _devices.toList()
        } else {
            _devices.filter { it.group == group }
        }
    }
    
    fun addDevice(device: Device): Device {
        val newDevice = device.copy(id = Device.generateId())
        _devices.add(newDevice)
        saveData()
        return newDevice
    }
    
    fun updateDevice(id: String, updatedDevice: Device): Device? {
        val index = _devices.indexOfFirst { it.id == id }
        if (index == -1) return null
        
        _devices[index] = updatedDevice.copy(id = id)
        saveData()
        return _devices[index]
    }
    
    fun deleteDevice(id: String): Boolean {
        val removed = _devices.removeAll { it.id == id }
        if (removed) saveData()
        return removed
    }
    
    fun getDevice(id: String): Device? {
        return _devices.find { it.id == id }
    }
    
    fun searchDevices(query: String, group: String? = null): List<Device> {
        val devices = getDevices(group)
        if (query.isBlank()) return devices
        
        val searchTerm = query.lowercase().trim()
        return devices.filter { device ->
            device.name.lowercase().contains(searchTerm) ||
            device.code.lowercase().contains(searchTerm) ||
            device.password.lowercase().contains(searchTerm) ||
            device.group.lowercase().contains(searchTerm)
        }
    }
    
    fun getGroupDeviceCount(groupName: String): Int {
        return _devices.count { it.group == groupName }
    }
    
    companion object {
        private const val PREFS_NAME = "remote_control_prefs"
        private const val KEY_GROUPS = "groups"
        private const val KEY_DEVICES = "devices"
        
        @Volatile
        private var INSTANCE: DataRepository? = null
        
        fun getInstance(context: Context): DataRepository {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: DataRepository(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
}
