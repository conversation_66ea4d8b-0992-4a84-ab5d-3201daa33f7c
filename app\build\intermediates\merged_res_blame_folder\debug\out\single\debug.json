[{"merged": "com.weihai.remotecontrol.app-merged_res-49:/xml_data_extraction_rules.xml.flat", "source": "com.weihai.remotecontrol.app-main-51:/xml/data_extraction_rules.xml"}, {"merged": "com.weihai.remotecontrol.app-merged_res-49:/mipmap-xxxhdpi_ic_launcher_round.xml.flat", "source": "com.weihai.remotecontrol.app-main-51:/mipmap-xxxhdpi/ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-merged_res-49:\\menu_bottom_navigation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-main-51:\\menu\\bottom_navigation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-merged_res-49:\\drawable_circle_primary_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-main-51:\\drawable\\circle_primary_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-merged_res-49:\\drawable_ic_devices_large.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-main-51:\\drawable\\ic_devices_large.xml"}, {"merged": "com.weihai.remotecontrol.app-merged_res-49:/mipmap-hdpi_ic_launcher.xml.flat", "source": "com.weihai.remotecontrol.app-main-51:/mipmap-hdpi/ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-merged_res-49:\\layout_item_group_statistic.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-main-51:\\layout\\item_group_statistic.xml"}, {"merged": "com.weihai.remotecontrol.app-merged_res-49:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "com.weihai.remotecontrol.app-main-51:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "com.weihai.remotecontrol.app-merged_res-49:/mipmap-xxxhdpi_ic_launcher.xml.flat", "source": "com.weihai.remotecontrol.app-main-51:/mipmap-xxxhdpi/ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-merged_res-49:\\layout_fragment_statistics.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-main-51:\\layout\\fragment_statistics.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-merged_res-49:\\drawable_ic_all_devices.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-main-51:\\drawable\\ic_all_devices.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-merged_res-49:\\drawable_ic_copy.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-main-51:\\drawable\\ic_copy.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-merged_res-49:\\layout_dialog_add_device.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-main-51:\\layout\\dialog_add_device.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-merged_res-49:\\drawable_ic_statistics.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-main-51:\\drawable\\ic_statistics.xml"}, {"merged": "com.weihai.remotecontrol.app-merged_res-49:/xml_backup_rules.xml.flat", "source": "com.weihai.remotecontrol.app-main-51:/xml/backup_rules.xml"}, {"merged": "com.weihai.remotecontrol.app-merged_res-49:/drawable_ic_launcher_foreground.xml.flat", "source": "com.weihai.remotecontrol.app-main-51:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.weihai.remotecontrol.app-merged_res-49:/mipmap-xxhdpi_ic_launcher_round.xml.flat", "source": "com.weihai.remotecontrol.app-main-51:/mipmap-xxhdpi/ic_launcher_round.xml"}, {"merged": "com.weihai.remotecontrol.app-merged_res-49:/mipmap-xxhdpi_ic_launcher.xml.flat", "source": "com.weihai.remotecontrol.app-main-51:/mipmap-xxhdpi/ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-merged_res-49:\\drawable_ic_folder.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-main-51:\\drawable\\ic_folder.xml"}, {"merged": "com.weihai.remotecontrol.app-merged_res-49:/drawable_ic_launcher_background.xml.flat", "source": "com.weihai.remotecontrol.app-main-51:/drawable/ic_launcher_background.xml"}, {"merged": "com.weihai.remotecontrol.app-merged_res-49:/mipmap-xhdpi_ic_launcher_round.xml.flat", "source": "com.weihai.remotecontrol.app-main-51:/mipmap-xhdpi/ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-merged_res-49:\\drawable_ic_server.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-main-51:\\drawable\\ic_server.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-merged_res-49:\\layout_fragment_devices.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-main-51:\\layout\\fragment_devices.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-merged_res-49:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-main-51:\\layout\\activity_main.xml"}, {"merged": "com.weihai.remotecontrol.app-merged_res-49:/mipmap-mdpi_ic_launcher.xml.flat", "source": "com.weihai.remotecontrol.app-main-51:/mipmap-mdpi/ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-merged_res-49:\\color_bottom_nav_color.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-main-51:\\color\\bottom_nav_color.xml"}, {"merged": "com.weihai.remotecontrol.app-merged_res-49:/mipmap-hdpi_ic_launcher_round.xml.flat", "source": "com.weihai.remotecontrol.app-main-51:/mipmap-hdpi/ic_launcher_round.xml"}, {"merged": "com.weihai.remotecontrol.app-merged_res-49:/drawable_ic_launcher_legacy.xml.flat", "source": "com.weihai.remotecontrol.app-main-51:/drawable/ic_launcher_legacy.xml"}, {"merged": "com.weihai.remotecontrol.app-merged_res-49:/mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "com.weihai.remotecontrol.app-main-51:/mipmap-anydpi-v26/ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-merged_res-49:\\drawable_ic_delete.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-main-51:\\drawable\\ic_delete.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-merged_res-49:\\drawable_ic_add.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-main-51:\\drawable\\ic_add.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-merged_res-49:\\layout_item_device.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-main-51:\\layout\\item_device.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-merged_res-49:\\drawable_ic_edit.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-main-51:\\drawable\\ic_edit.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-merged_res-49:\\drawable_ic_folder_add.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-main-51:\\drawable\\ic_folder_add.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-merged_res-49:\\drawable_ic_devices.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-main-51:\\drawable\\ic_devices.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-merged_res-49:\\drawable_ic_clear.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-main-51:\\drawable\\ic_clear.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-merged_res-49:\\drawable_ic_search.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.weihai.remotecontrol.app-main-51:\\drawable\\ic_search.xml"}, {"merged": "com.weihai.remotecontrol.app-merged_res-49:/mipmap-mdpi_ic_launcher_round.xml.flat", "source": "com.weihai.remotecontrol.app-main-51:/mipmap-mdpi/ic_launcher_round.xml"}, {"merged": "com.weihai.remotecontrol.app-merged_res-49:/mipmap-xhdpi_ic_launcher.xml.flat", "source": "com.weihai.remotecontrol.app-main-51:/mipmap-xhdpi/ic_launcher.xml"}]