<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/CardStyle"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="12dp"
    android:layout_marginVertical="4dp"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp">

        <!-- 第一行：设备名称、分组和类型标签 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvDeviceName"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textColor="@color/dark"
                android:textSize="16sp"
                android:textStyle="bold"
                android:maxLines="1"
                android:ellipsize="end"
                tools:text="经区皂埠村西自动机场" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chipGroup"
                style="@style/GroupChip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="4dp"
                tools:text="经区" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chipType"
                style="@style/StandardChip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="标准" />

        </LinearLayout>

        <!-- 第二行：连接码和密码信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <!-- 连接码部分 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/device_code"
                    android:textColor="@color/gray_500"
                    android:textSize="12sp"
                    android:layout_marginEnd="4dp" />

                <TextView
                    android:id="@+id/tvDeviceCode"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textColor="@color/dark"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    tools:text="60516419" />

                <ImageButton
                    android:id="@+id/btnCopyCode"
                    android:layout_width="28dp"
                    android:layout_height="28dp"
                    android:layout_marginStart="4dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:src="@drawable/ic_copy"
                    android:tint="@color/primary" />

            </LinearLayout>

            <!-- 分隔线 -->
            <View
                android:layout_width="1dp"
                android:layout_height="20dp"
                android:layout_marginHorizontal="12dp"
                android:background="@color/gray_200" />

            <!-- 连接密码部分 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/device_password"
                    android:textColor="@color/gray_500"
                    android:textSize="12sp"
                    android:layout_marginEnd="4dp" />

                <TextView
                    android:id="@+id/tvDevicePassword"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textColor="@color/dark"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    tools:text="972745" />

                <ImageButton
                    android:id="@+id/btnCopyPassword"
                    android:layout_width="28dp"
                    android:layout_height="28dp"
                    android:layout_marginStart="4dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:src="@drawable/ic_copy"
                    android:tint="@color/primary" />

            </LinearLayout>

            <!-- 操作按钮 -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:orientation="horizontal">

                <ImageButton
                    android:id="@+id/btnEditDevice"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_marginEnd="4dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:src="@drawable/ic_edit"
                    android:tint="@color/warning" />

                <ImageButton
                    android:id="@+id/btnDeleteDevice"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:src="@drawable/ic_delete"
                    android:tint="@color/danger" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
