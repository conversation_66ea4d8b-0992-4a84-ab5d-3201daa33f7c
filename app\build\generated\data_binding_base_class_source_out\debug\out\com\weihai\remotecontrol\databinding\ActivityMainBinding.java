// Generated by view binder compiler. Do not edit!
package com.weihai.remotecontrol.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import androidx.viewpager2.widget.ViewPager2;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import com.weihai.remotecontrol.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final AppBarLayout appBarLayout;

  @NonNull
  public final BottomNavigationView bottomNavigation;

  @NonNull
  public final Button btnAddDevice;

  @NonNull
  public final Button btnAddGroup;

  @NonNull
  public final TextInputEditText etSearch;

  @NonNull
  public final TextInputLayout searchInputLayout;

  @NonNull
  public final ViewPager2 viewPager;

  private ActivityMainBinding(@NonNull ConstraintLayout rootView,
      @NonNull AppBarLayout appBarLayout, @NonNull BottomNavigationView bottomNavigation,
      @NonNull Button btnAddDevice, @NonNull Button btnAddGroup,
      @NonNull TextInputEditText etSearch, @NonNull TextInputLayout searchInputLayout,
      @NonNull ViewPager2 viewPager) {
    this.rootView = rootView;
    this.appBarLayout = appBarLayout;
    this.bottomNavigation = bottomNavigation;
    this.btnAddDevice = btnAddDevice;
    this.btnAddGroup = btnAddGroup;
    this.etSearch = etSearch;
    this.searchInputLayout = searchInputLayout;
    this.viewPager = viewPager;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.appBarLayout;
      AppBarLayout appBarLayout = ViewBindings.findChildViewById(rootView, id);
      if (appBarLayout == null) {
        break missingId;
      }

      id = R.id.bottomNavigation;
      BottomNavigationView bottomNavigation = ViewBindings.findChildViewById(rootView, id);
      if (bottomNavigation == null) {
        break missingId;
      }

      id = R.id.btnAddDevice;
      Button btnAddDevice = ViewBindings.findChildViewById(rootView, id);
      if (btnAddDevice == null) {
        break missingId;
      }

      id = R.id.btnAddGroup;
      Button btnAddGroup = ViewBindings.findChildViewById(rootView, id);
      if (btnAddGroup == null) {
        break missingId;
      }

      id = R.id.etSearch;
      TextInputEditText etSearch = ViewBindings.findChildViewById(rootView, id);
      if (etSearch == null) {
        break missingId;
      }

      id = R.id.searchInputLayout;
      TextInputLayout searchInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (searchInputLayout == null) {
        break missingId;
      }

      id = R.id.viewPager;
      ViewPager2 viewPager = ViewBindings.findChildViewById(rootView, id);
      if (viewPager == null) {
        break missingId;
      }

      return new ActivityMainBinding((ConstraintLayout) rootView, appBarLayout, bottomNavigation,
          btnAddDevice, btnAddGroup, etSearch, searchInputLayout, viewPager);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
