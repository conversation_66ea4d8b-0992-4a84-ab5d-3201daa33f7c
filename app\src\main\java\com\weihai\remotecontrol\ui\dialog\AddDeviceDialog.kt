package com.weihai.remotecontrol.ui.dialog

import android.app.Dialog
import android.os.Bundle
import android.widget.ArrayAdapter
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import com.weihai.remotecontrol.R
import com.weihai.remotecontrol.data.DataRepository
import com.weihai.remotecontrol.data.Device
import com.weihai.remotecontrol.data.DeviceType
import com.weihai.remotecontrol.databinding.DialogAddDeviceBinding

/**
 * 添加设备对话框
 */
class AddDeviceDialog : DialogFragment() {
    
    private var _binding: DialogAddDeviceBinding? = null
    private val binding get() = _binding!!
    
    private var onSaveCallback: ((Device) -> Unit)? = null
    
    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        _binding = DialogAddDeviceBinding.inflate(layoutInflater)
        
        setupDropdowns()
        setupClickListeners()
        
        return AlertDialog.Builder(requireContext())
            .setView(binding.root)
            .create()
    }
    
    private fun setupDropdowns() {
        // 设置分组下拉菜单
        val repository = DataRepository.getInstance(requireContext())
        val groups = repository.groups
        val groupAdapter = ArrayAdapter(requireContext(), android.R.layout.simple_dropdown_item_1line, groups)
        binding.actvDeviceGroup.setAdapter(groupAdapter)
        if (groups.isNotEmpty()) {
            binding.actvDeviceGroup.setText(groups[0], false)
        }
        
        // 设置设备类型下拉菜单
        val deviceTypes = listOf("标准", "室内")
        val typeAdapter = ArrayAdapter(requireContext(), android.R.layout.simple_dropdown_item_1line, deviceTypes)
        binding.actvDeviceType.setAdapter(typeAdapter)
        binding.actvDeviceType.setText("标准", false)
    }
    
    private fun setupClickListeners() {
        binding.btnCancel.setOnClickListener {
            dismiss()
        }
        
        binding.btnSave.setOnClickListener {
            if (validateInput()) {
                val device = createDevice()
                onSaveCallback?.invoke(device)
                dismiss()
            }
        }
    }
    
    private fun validateInput(): Boolean {
        var isValid = true
        
        // 验证设备名称
        if (binding.etDeviceName.text.isNullOrBlank()) {
            binding.tilDeviceName.error = getString(R.string.required_field)
            isValid = false
        } else {
            binding.tilDeviceName.error = null
        }
        
        // 验证连接码
        if (binding.etDeviceCode.text.isNullOrBlank()) {
            binding.tilDeviceCode.error = getString(R.string.required_field)
            isValid = false
        } else {
            binding.tilDeviceCode.error = null
        }
        
        // 验证连接密码
        if (binding.etDevicePassword.text.isNullOrBlank()) {
            binding.tilDevicePassword.error = getString(R.string.required_field)
            isValid = false
        } else {
            binding.tilDevicePassword.error = null
        }
        
        return isValid
    }
    
    private fun createDevice(): Device {
        val name = binding.etDeviceName.text.toString().trim()
        val group = binding.actvDeviceGroup.text.toString()
        val typeText = binding.actvDeviceType.text.toString()
        val type = if (typeText == "室内") DeviceType.INDOOR else DeviceType.STANDARD
        val code = binding.etDeviceCode.text.toString().trim()
        val password = binding.etDevicePassword.text.toString().trim()
        
        return Device(
            id = Device.generateId(),
            name = name,
            group = group,
            type = type,
            code = code,
            password = password
        )
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
    
    companion object {
        fun show(fragmentManager: FragmentManager, onSave: (Device) -> Unit) {
            val dialog = AddDeviceDialog()
            dialog.onSaveCallback = onSave
            dialog.show(fragmentManager, "AddDeviceDialog")
        }
    }
}
