%com.weihai.remotecontrol.MainActivity6com.weihai.remotecontrol.MainActivity.ViewPagerAdapter(com.weihai.remotecontrol.data.DeviceType1com.weihai.remotecontrol.ui.adapter.DeviceAdapterBcom.weihai.remotecontrol.ui.adapter.DeviceAdapter.DeviceViewHolder6com.weihai.remotecontrol.ui.adapter.DeviceDiffCallback9com.weihai.remotecontrol.ui.adapter.GroupStatisticAdapterDcom.weihai.remotecontrol.ui.adapter.GroupStatisticAdapter.ViewHolder>com.weihai.remotecontrol.ui.adapter.GroupStatisticDiffCallback2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog1com.weihai.remotecontrol.ui.dialog.AddGroupDialog6com.weihai.remotecontrol.ui.dialog.DeleteConfirmDialog3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog2com.weihai.remotecontrol.ui.dialog.EditGroupDialog4com.weihai.remotecontrol.ui.fragment.DevicesFragment7com.weihai.remotecontrol.ui.fragment.StatisticsFragment3com.weihai.remotecontrol.ui.viewmodel.MainViewModel6com.weihai.remotecontrol.databinding.ItemDeviceBinding;com.weihai.remotecontrol.databinding.DialogAddDeviceBinding>com.weihai.remotecontrol.databinding.ItemGroupStatisticBinding>com.weihai.remotecontrol.databinding.FragmentStatisticsBinding8com.weihai.remotecontrol.databinding.ActivityMainBinding;com.weihai.remotecontrol.databinding.FragmentDevicesBinding                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      