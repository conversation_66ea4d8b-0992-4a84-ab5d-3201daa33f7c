package com.weihai.remotecontrol.ui.dialog

import android.app.Dialog
import android.os.Bundle
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import com.weihai.remotecontrol.R

/**
 * 删除确认对话框
 */
class DeleteConfirmDialog : DialogFragment() {
    
    private var title: String = ""
    private var message: String = ""
    private var onConfirmCallback: (() -> Unit)? = null
    
    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        return AlertDialog.Builder(requireContext())
            .setTitle(title)
            .setMessage(message)
            .setIcon(R.drawable.ic_delete)
            .setPositiveButton(R.string.delete) { _, _ ->
                onConfirmCallback?.invoke()
            }
            .setNegativeButton(R.string.cancel, null)
            .create()
    }
    
    companion object {
        fun show(
            fragmentManager: FragmentManager,
            title: String,
            message: String,
            onConfirm: () -> Unit
        ) {
            val dialog = DeleteConfirmDialog()
            dialog.title = title
            dialog.message = message
            dialog.onConfirmCallback = onConfirm
            dialog.show(fragmentManager, "DeleteConfirmDialog")
        }
    }
}
