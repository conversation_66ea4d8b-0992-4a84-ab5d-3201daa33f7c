package com.weihai.remotecontrol.ui.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.weihai.remotecontrol.databinding.FragmentStatisticsBinding
import com.weihai.remotecontrol.ui.adapter.GroupStatisticAdapter
import com.weihai.remotecontrol.ui.viewmodel.MainViewModel

/**
 * 统计Fragment
 */
class StatisticsFragment : Fragment() {
    
    private var _binding: FragmentStatisticsBinding? = null
    private val binding get() = _binding!!
    
    private val viewModel: MainViewModel by activityViewModels()
    private lateinit var groupStatisticAdapter: GroupStatisticAdapter
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentStatisticsBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupRecyclerView()
        observeViewModel()
    }
    
    private fun setupRecyclerView() {
        groupStatisticAdapter = GroupStatisticAdapter()
        binding.recyclerViewGroupStats.apply {
            adapter = groupStatisticAdapter
            layoutManager = LinearLayoutManager(requireContext())
        }
    }
    
    private fun observeViewModel() {
        // 观察总设备数
        viewModel.totalDevices.observe(viewLifecycleOwner) { count ->
            binding.tvTotalDevices.text = count.toString()
        }
        
        // 观察分组数量
        viewModel.totalGroups.observe(viewLifecycleOwner) { count ->
            binding.tvTotalGroups.text = count.toString()
        }
        
        // 观察室内设备数
        viewModel.indoorDevices.observe(viewLifecycleOwner) { count ->
            binding.tvIndoorDevices.text = count.toString()
        }
        
        // 观察标准设备数
        viewModel.standardDevices.observe(viewLifecycleOwner) { count ->
            binding.tvStandardDevices.text = count.toString()
        }
        
        // 观察设备数据变化，更新分组统计
        viewModel.devices.observe(viewLifecycleOwner) {
            updateGroupStatistics()
        }
        
        // 观察分组数据变化，更新分组统计
        viewModel.groups.observe(viewLifecycleOwner) {
            updateGroupStatistics()
        }
    }
    
    private fun updateGroupStatistics() {
        val statistics = viewModel.getGroupStatistics()
        groupStatisticAdapter.submitList(statistics)
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
