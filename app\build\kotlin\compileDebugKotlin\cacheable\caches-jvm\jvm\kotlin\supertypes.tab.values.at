/ Header Record For PersistentHashMapValueStorage) (androidx.appcompat.app.AppCompatActivity1 0androidx.viewpager2.adapter.FragmentStateAdapter kotlin.Enum) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback% $androidx.fragment.app.DialogFragment% $androidx.fragment.app.DialogFragment% $androidx.fragment.app.DialogFragment% $androidx.fragment.app.DialogFragment% $androidx.fragment.app.DialogFragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment$ #androidx.lifecycle.AndroidViewModel!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding androidx.fragment.app.Fragment