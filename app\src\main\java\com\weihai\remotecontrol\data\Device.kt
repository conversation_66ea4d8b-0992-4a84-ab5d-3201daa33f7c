package com.weihai.remotecontrol.data

import com.google.gson.annotations.SerializedName

/**
 * 设备数据模型
 */
data class Device(
    @SerializedName("id")
    val id: String,
    
    @SerializedName("name")
    var name: String,
    
    @SerializedName("group")
    var group: String,
    
    @SerializedName("type")
    var type: DeviceType = DeviceType.STANDARD,
    
    @SerializedName("code")
    var code: String,
    
    @SerializedName("password")
    var password: String
) {
    companion object {
        fun generateId(): String = System.currentTimeMillis().toString()
    }
}

/**
 * 设备类型枚举
 */
enum class DeviceType(val value: String, val displayName: String) {
    @SerializedName("standard")
    STANDARD("standard", "标准"),
    
    @SerializedName("indoor")
    INDOOR("indoor", "室内");
    
    companion object {
        fun fromValue(value: String): DeviceType {
            return values().find { it.value == value } ?: STANDARD
        }
    }
}
