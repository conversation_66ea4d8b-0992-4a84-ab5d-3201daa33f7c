<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- 分组选项卡 -->
    <HorizontalScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:scrollbars="none">

        <com.google.android.material.chip.ChipGroup
            android:id="@+id/chipGroupTabs"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:chipSpacingHorizontal="8dp"
            app:selectionRequired="true"
            app:singleSelection="true">

            <com.google.android.material.chip.Chip
                android:id="@+id/chipAllDevices"
                style="@style/Widget.Material3.Chip.Filter"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:checked="true"
                android:text="@string/all_devices"
                app:chipIcon="@drawable/ic_all_devices" />

        </com.google.android.material.chip.ChipGroup>

    </HorizontalScrollView>

    <!-- 设备列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewDevices"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipToPadding="false"
        android:paddingBottom="16dp" />

    <!-- 空状态视图 -->
    <LinearLayout
        android:id="@+id/layoutEmpty"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone">

        <ImageView
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginBottom="16dp"
            android:alpha="0.3"
            android:src="@drawable/ic_devices_large"
            android:tint="@color/gray_400" />

        <TextView
            android:id="@+id/tvEmptyMessage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/no_devices_in_group"
            android:textColor="@color/gray_400"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/tvEmptyTip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="@string/search_tip"
            android:textColor="@color/gray_400"
            android:textSize="14sp"
            android:visibility="gone" />

    </LinearLayout>

</LinearLayout>
