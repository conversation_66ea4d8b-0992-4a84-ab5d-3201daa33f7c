// Generated by view binder compiler. Do not edit!
package com.weihai.remotecontrol.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.weihai.remotecontrol.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemGroupStatisticBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView tvDeviceCount;

  @NonNull
  public final TextView tvGroupName;

  @NonNull
  public final TextView tvIndoorCount;

  @NonNull
  public final TextView tvStandardCount;

  private ItemGroupStatisticBinding(@NonNull LinearLayout rootView, @NonNull TextView tvDeviceCount,
      @NonNull TextView tvGroupName, @NonNull TextView tvIndoorCount,
      @NonNull TextView tvStandardCount) {
    this.rootView = rootView;
    this.tvDeviceCount = tvDeviceCount;
    this.tvGroupName = tvGroupName;
    this.tvIndoorCount = tvIndoorCount;
    this.tvStandardCount = tvStandardCount;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemGroupStatisticBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemGroupStatisticBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_group_statistic, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemGroupStatisticBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.tvDeviceCount;
      TextView tvDeviceCount = ViewBindings.findChildViewById(rootView, id);
      if (tvDeviceCount == null) {
        break missingId;
      }

      id = R.id.tvGroupName;
      TextView tvGroupName = ViewBindings.findChildViewById(rootView, id);
      if (tvGroupName == null) {
        break missingId;
      }

      id = R.id.tvIndoorCount;
      TextView tvIndoorCount = ViewBindings.findChildViewById(rootView, id);
      if (tvIndoorCount == null) {
        break missingId;
      }

      id = R.id.tvStandardCount;
      TextView tvStandardCount = ViewBindings.findChildViewById(rootView, id);
      if (tvStandardCount == null) {
        break missingId;
      }

      return new ItemGroupStatisticBinding((LinearLayout) rootView, tvDeviceCount, tvGroupName,
          tvIndoorCount, tvStandardCount);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
