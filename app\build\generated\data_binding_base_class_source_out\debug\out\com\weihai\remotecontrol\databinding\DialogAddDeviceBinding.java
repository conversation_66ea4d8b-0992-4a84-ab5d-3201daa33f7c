// Generated by view binder compiler. Do not edit!
package com.weihai.remotecontrol.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AutoCompleteTextView;
import android.widget.Button;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import com.weihai.remotecontrol.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogAddDeviceBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final AutoCompleteTextView actvDeviceGroup;

  @NonNull
  public final AutoCompleteTextView actvDeviceType;

  @NonNull
  public final Button btnCancel;

  @NonNull
  public final Button btnSave;

  @NonNull
  public final TextInputEditText etDeviceCode;

  @NonNull
  public final TextInputEditText etDeviceName;

  @NonNull
  public final TextInputEditText etDevicePassword;

  @NonNull
  public final TextInputLayout tilDeviceCode;

  @NonNull
  public final TextInputLayout tilDeviceGroup;

  @NonNull
  public final TextInputLayout tilDeviceName;

  @NonNull
  public final TextInputLayout tilDevicePassword;

  @NonNull
  public final TextInputLayout tilDeviceType;

  private DialogAddDeviceBinding(@NonNull LinearLayout rootView,
      @NonNull AutoCompleteTextView actvDeviceGroup, @NonNull AutoCompleteTextView actvDeviceType,
      @NonNull Button btnCancel, @NonNull Button btnSave, @NonNull TextInputEditText etDeviceCode,
      @NonNull TextInputEditText etDeviceName, @NonNull TextInputEditText etDevicePassword,
      @NonNull TextInputLayout tilDeviceCode, @NonNull TextInputLayout tilDeviceGroup,
      @NonNull TextInputLayout tilDeviceName, @NonNull TextInputLayout tilDevicePassword,
      @NonNull TextInputLayout tilDeviceType) {
    this.rootView = rootView;
    this.actvDeviceGroup = actvDeviceGroup;
    this.actvDeviceType = actvDeviceType;
    this.btnCancel = btnCancel;
    this.btnSave = btnSave;
    this.etDeviceCode = etDeviceCode;
    this.etDeviceName = etDeviceName;
    this.etDevicePassword = etDevicePassword;
    this.tilDeviceCode = tilDeviceCode;
    this.tilDeviceGroup = tilDeviceGroup;
    this.tilDeviceName = tilDeviceName;
    this.tilDevicePassword = tilDevicePassword;
    this.tilDeviceType = tilDeviceType;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogAddDeviceBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogAddDeviceBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_add_device, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogAddDeviceBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.actvDeviceGroup;
      AutoCompleteTextView actvDeviceGroup = ViewBindings.findChildViewById(rootView, id);
      if (actvDeviceGroup == null) {
        break missingId;
      }

      id = R.id.actvDeviceType;
      AutoCompleteTextView actvDeviceType = ViewBindings.findChildViewById(rootView, id);
      if (actvDeviceType == null) {
        break missingId;
      }

      id = R.id.btnCancel;
      Button btnCancel = ViewBindings.findChildViewById(rootView, id);
      if (btnCancel == null) {
        break missingId;
      }

      id = R.id.btnSave;
      Button btnSave = ViewBindings.findChildViewById(rootView, id);
      if (btnSave == null) {
        break missingId;
      }

      id = R.id.etDeviceCode;
      TextInputEditText etDeviceCode = ViewBindings.findChildViewById(rootView, id);
      if (etDeviceCode == null) {
        break missingId;
      }

      id = R.id.etDeviceName;
      TextInputEditText etDeviceName = ViewBindings.findChildViewById(rootView, id);
      if (etDeviceName == null) {
        break missingId;
      }

      id = R.id.etDevicePassword;
      TextInputEditText etDevicePassword = ViewBindings.findChildViewById(rootView, id);
      if (etDevicePassword == null) {
        break missingId;
      }

      id = R.id.tilDeviceCode;
      TextInputLayout tilDeviceCode = ViewBindings.findChildViewById(rootView, id);
      if (tilDeviceCode == null) {
        break missingId;
      }

      id = R.id.tilDeviceGroup;
      TextInputLayout tilDeviceGroup = ViewBindings.findChildViewById(rootView, id);
      if (tilDeviceGroup == null) {
        break missingId;
      }

      id = R.id.tilDeviceName;
      TextInputLayout tilDeviceName = ViewBindings.findChildViewById(rootView, id);
      if (tilDeviceName == null) {
        break missingId;
      }

      id = R.id.tilDevicePassword;
      TextInputLayout tilDevicePassword = ViewBindings.findChildViewById(rootView, id);
      if (tilDevicePassword == null) {
        break missingId;
      }

      id = R.id.tilDeviceType;
      TextInputLayout tilDeviceType = ViewBindings.findChildViewById(rootView, id);
      if (tilDeviceType == null) {
        break missingId;
      }

      return new DialogAddDeviceBinding((LinearLayout) rootView, actvDeviceGroup, actvDeviceType,
          btnCancel, btnSave, etDeviceCode, etDeviceName, etDevicePassword, tilDeviceCode,
          tilDeviceGroup, tilDeviceName, tilDevicePassword, tilDeviceType);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
