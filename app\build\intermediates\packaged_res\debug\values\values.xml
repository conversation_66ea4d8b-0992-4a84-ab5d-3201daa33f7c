<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="black">#000000</color>
    <color name="danger">#F87272</color>
    <color name="dark">#1E293B</color>
    <color name="delete_zone_bg">#FEE2E2</color>
    <color name="delete_zone_border">#EF4444</color>
    <color name="delete_zone_text">#DC2626</color>
    <color name="gray_100">#F3F4F6</color>
    <color name="gray_200">#E5E7EB</color>
    <color name="gray_300">#D1D5DB</color>
    <color name="gray_400">#9CA3AF</color>
    <color name="gray_50">#F9FAFB</color>
    <color name="gray_500">#6B7280</color>
    <color name="gray_600">#4B5563</color>
    <color name="gray_700">#374151</color>
    <color name="gray_800">#1F2937</color>
    <color name="gray_900">#111827</color>
    <color name="indoor_bg">#DBEAFE</color>
    <color name="indoor_text">#2563EB</color>
    <color name="info">#165DFF</color>
    <color name="light">#F8FAFC</color>
    <color name="primary">#165DFF</color>
    <color name="primary_variant">#0D47CC</color>
    <color name="secondary">#36D399</color>
    <color name="secondary_variant">#2AB87A</color>
    <color name="standard_bg">#D1FAE5</color>
    <color name="standard_text">#059669</color>
    <color name="success">#36D399</color>
    <color name="transparent">#00000000</color>
    <color name="warning">#FF9F1C</color>
    <color name="white">#FFFFFF</color>
    <string name="add">新增</string>
    <string name="add_device">新增设备</string>
    <string name="add_group">新增分组</string>
    <string name="all_devices">全部设备</string>
    <string name="app_name">远程控制码管理中心</string>
    <string name="cancel">取消</string>
    <string name="confirm_delete">确认删除</string>
    <string name="confirm_delete_device">您确定要删除此设备吗？此操作无法撤销。</string>
    <string name="confirm_delete_group">确认删除分组</string>
    <string name="confirm_delete_group_empty">您确定要删除分组"%s"吗？此操作无法撤销。</string>
    <string name="confirm_delete_group_message">您确定要删除分组"%s"吗？该分组下有 %d 个设备，删除后这些设备将移动到"默认分组"。此操作无法撤销。</string>
    <string name="copy">复制</string>
    <string name="copy_success">复制成功！</string>
    <string name="create">创建</string>
    <string name="data_statistics">数据统计</string>
    <string name="default_group">默认分组</string>
    <string name="delete">删除</string>
    <string name="device_code">连接码</string>
    <string name="device_count">共 %d 个设备</string>
    <string name="device_group">所属分组</string>
    <string name="device_management">设备管理</string>
    <string name="device_name">设备名称</string>
    <string name="device_password">连接密码</string>
    <string name="device_type">设备类型</string>
    <string name="device_type_indoor">室内</string>
    <string name="device_type_standard">标准</string>
    <string name="drag_to_delete">拖拽到此处删除分组</string>
    <string name="edit">编辑</string>
    <string name="group_detail_statistics">分组详细统计</string>
    <string name="group_name">分组名称</string>
    <string name="group_name_exists">该分组名称已存在！</string>
    <string name="indoor_devices">室内设备</string>
    <string name="no_devices_in_group">当前分组暂无设备，请添加设备</string>
    <string name="no_group_data">暂无分组数据</string>
    <string name="no_search_results">未找到包含"%s"的设备</string>
    <string name="required_field">必填项</string>
    <string name="save">保存</string>
    <string name="search_hint">搜索设备名称、连接码或密码...</string>
    <string name="search_tip">尝试使用其他关键词搜索</string>
    <string name="standard_devices">标准设备</string>
    <string name="statistics">统计</string>
    <string name="statistics_description">查看设备和分组的统计信息</string>
    <string name="total_devices">总设备数</string>
    <string name="total_groups">分组数量</string>
    <style name="CardStyle">
        <item name="android:layout_margin">8dp</item>
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">2dp</item>
        <item name="android:background">@color/white</item>
    </style>
    <style name="ChipStyle" parent="Widget.Material3.Chip.Assist">
        <item name="chipCornerRadius">16dp</item>
        <item name="android:textSize">12sp</item>
    </style>
    <style name="GroupChip" parent="ChipStyle">
        <item name="chipBackgroundColor">@color/gray_100</item>
        <item name="android:textColor">@color/gray_600</item>
    </style>
    <style name="IndoorChip" parent="ChipStyle">
        <item name="chipBackgroundColor">@color/indoor_bg</item>
        <item name="android:textColor">@color/indoor_text</item>
    </style>
    <style name="InputStyle" parent="Widget.Material3.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">@color/primary</item>
        <item name="boxCornerRadiusTopStart">8dp</item>
        <item name="boxCornerRadiusTopEnd">8dp</item>
        <item name="boxCornerRadiusBottomStart">8dp</item>
        <item name="boxCornerRadiusBottomEnd">8dp</item>
    </style>
    <style name="PrimaryButton" parent="Widget.Material3.Button">
        <item name="android:textColor">@color/white</item>
        <item name="backgroundTint">@color/primary</item>
        <item name="cornerRadius">8dp</item>
    </style>
    <style name="SecondaryButton" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:textColor">@color/gray_700</item>
        <item name="strokeColor">@color/gray_300</item>
        <item name="cornerRadius">8dp</item>
    </style>
    <style name="StandardChip" parent="ChipStyle">
        <item name="chipBackgroundColor">@color/standard_bg</item>
        <item name="android:textColor">@color/standard_text</item>
    </style>
    <style name="Theme.RemoteControl" parent="Theme.Material3.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_variant</item>
        <item name="colorSecondary">@color/secondary</item>
        <item name="colorSecondaryVariant">@color/secondary_variant</item>
        <item name="android:colorBackground">@color/gray_50</item>
        <item name="colorSurface">@color/white</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="colorOnBackground">@color/dark</item>
        <item name="colorOnSurface">@color/dark</item>
        <item name="android:statusBarColor">@color/white</item>
        <item name="android:windowLightStatusBar">true</item>
    </style>
</resources>