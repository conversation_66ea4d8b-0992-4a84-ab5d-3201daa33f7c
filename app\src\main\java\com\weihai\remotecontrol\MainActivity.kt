package com.weihai.remotecontrol

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.google.android.material.tabs.TabLayoutMediator
import com.weihai.remotecontrol.databinding.ActivityMainBinding
import com.weihai.remotecontrol.ui.dialog.AddDeviceDialog
import com.weihai.remotecontrol.ui.dialog.AddGroupDialog
import com.weihai.remotecontrol.ui.dialog.DeleteConfirmDialog
import com.weihai.remotecontrol.ui.dialog.EditDeviceDialog
import com.weihai.remotecontrol.ui.dialog.EditGroupDialog
import com.weihai.remotecontrol.ui.fragment.DevicesFragment
import com.weihai.remotecontrol.ui.fragment.StatisticsFragment
import com.weihai.remotecontrol.ui.viewmodel.MainViewModel

class MainActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityMainBinding
    private val viewModel: MainViewModel by viewModels()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupViewPager()
        setupBottomNavigation()
        setupSearchView()
        setupClickListeners()
        observeViewModel()
    }
    
    private fun setupViewPager() {
        val adapter = ViewPagerAdapter()
        binding.viewPager.adapter = adapter
        binding.viewPager.isUserInputEnabled = false // 禁用滑动
    }
    
    private fun setupBottomNavigation() {
        binding.bottomNavigation.setOnItemSelectedListener { item ->
            when (item.itemId) {
                R.id.nav_devices -> {
                    binding.viewPager.currentItem = 0
                    true
                }
                R.id.nav_statistics -> {
                    binding.viewPager.currentItem = 1
                    true
                }
                else -> false
            }
        }
    }
    
    private fun setupSearchView() {
        binding.etSearch.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                viewModel.setSearchQuery(s?.toString() ?: "")
            }
        })
    }
    
    private fun setupClickListeners() {
        binding.btnAddDevice.setOnClickListener {
            viewModel.showAddDeviceDialog()
        }
        
        binding.btnAddGroup.setOnClickListener {
            viewModel.showAddGroupDialog()
        }
    }
    
    private fun observeViewModel() {
        // 观察对话框显示事件
        viewModel.showAddDeviceDialog.observe(this) { show ->
            if (show) {
                AddDeviceDialog.show(supportFragmentManager) { device ->
                    viewModel.addDevice(device)
                    viewModel.hideAddDeviceDialog()
                }
            }
        }
        
        viewModel.showEditDeviceDialog.observe(this) { device ->
            device?.let {
                EditDeviceDialog.show(supportFragmentManager, it) { updatedDevice ->
                    viewModel.updateDevice(it.id, updatedDevice)
                    viewModel.hideEditDeviceDialog()
                }
            }
        }
        
        viewModel.showDeleteDeviceDialog.observe(this) { device ->
            device?.let {
                DeleteConfirmDialog.show(
                    supportFragmentManager,
                    "确认删除",
                    "您确定要删除设备\"${it.name}\"吗？此操作无法撤销。"
                ) {
                    viewModel.deleteDevice(it.id)
                    viewModel.hideDeleteDeviceDialog()
                }
            }
        }
        
        viewModel.showAddGroupDialog.observe(this) { show ->
            if (show) {
                AddGroupDialog.show(supportFragmentManager) { groupName ->
                    val success = viewModel.addGroup(groupName)
                    if (!success) {
                        // 显示错误提示
                    }
                    viewModel.hideAddGroupDialog()
                }
            }
        }
        
        viewModel.showEditGroupDialog.observe(this) { groupName ->
            groupName?.let {
                EditGroupDialog.show(supportFragmentManager, it) { newName ->
                    val success = viewModel.editGroup(it, newName)
                    if (!success) {
                        // 显示错误提示
                    }
                    viewModel.hideEditGroupDialog()
                }
            }
        }
        
        viewModel.showDeleteGroupDialog.observe(this) { groupName ->
            groupName?.let {
                val deviceCount = viewModel.getGroupDeviceCount(it)
                val message = if (deviceCount > 0) {
                    "您确定要删除分组\"$it\"吗？该分组下有 $deviceCount 个设备，删除后这些设备将移动到默认分组。此操作无法撤销。"
                } else {
                    "您确定要删除分组\"$it\"吗？此操作无法撤销。"
                }
                
                DeleteConfirmDialog.show(
                    supportFragmentManager,
                    "确认删除分组",
                    message
                ) {
                    viewModel.deleteGroup(it)
                    viewModel.hideDeleteGroupDialog()
                }
            }
        }
    }
    
    private inner class ViewPagerAdapter : FragmentStateAdapter(this) {
        override fun getItemCount(): Int = 2
        
        override fun createFragment(position: Int): Fragment {
            return when (position) {
                0 -> DevicesFragment()
                1 -> StatisticsFragment()
                else -> throw IllegalArgumentException("Invalid position: $position")
            }
        }
    }
}
