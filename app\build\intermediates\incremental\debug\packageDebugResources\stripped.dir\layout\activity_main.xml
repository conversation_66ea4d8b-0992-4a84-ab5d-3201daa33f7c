<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/gray_50"
    tools:context=".MainActivity">

    <!-- 顶部导航栏 -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        app:elevation="2dp"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 标题和按钮行 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginEnd="8dp"
                    android:src="@drawable/ic_server"
                    android:tint="@color/primary" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/app_name"
                    android:textColor="@color/primary"
                    android:textSize="20sp"
                    android:textStyle="bold" />

                <Button
                    android:id="@+id/btnAddGroup"
                    style="@style/SecondaryButton"
                    android:layout_width="wrap_content"
                    android:layout_height="36dp"
                    android:layout_marginEnd="8dp"
                    android:drawableStart="@drawable/ic_folder_add"
                    android:drawablePadding="4dp"
                    android:text="@string/add_group"
                    android:textSize="12sp" />

                <Button
                    android:id="@+id/btnAddDevice"
                    style="@style/PrimaryButton"
                    android:layout_width="wrap_content"
                    android:layout_height="36dp"
                    android:drawableStart="@drawable/ic_add"
                    android:drawablePadding="4dp"
                    android:text="@string/add_device"
                    android:textSize="12sp" />

            </LinearLayout>

            <!-- 搜索栏 -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/searchInputLayout"
                style="@style/InputStyle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                app:endIconDrawable="@drawable/ic_clear"
                app:endIconMode="clear_text"
                app:startIconDrawable="@drawable/ic_search">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etSearch"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/search_hint"
                    android:imeOptions="actionSearch"
                    android:inputType="text"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

        </LinearLayout>

    </com.google.android.material.appbar.AppBarLayout>

    <!-- 主内容区域 -->
    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewPager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/bottomNavigation"
        app:layout_constraintTop_toBottomOf="@+id/appBarLayout" />

    <!-- 底部导航 -->
    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bottomNavigation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        app:elevation="8dp"
        app:itemIconTint="@color/bottom_nav_color"
        app:itemTextColor="@color/bottom_nav_color"
        app:layout_constraintBottom_toBottomOf="parent"
        app:menu="@menu/bottom_navigation" />

</androidx.constraintlayout.widget.ConstraintLayout>
