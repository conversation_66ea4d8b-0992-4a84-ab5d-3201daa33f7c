package com.weihai.remotecontrol.ui.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.map
import com.weihai.remotecontrol.data.DataRepository
import com.weihai.remotecontrol.data.Device
import com.weihai.remotecontrol.data.DeviceType

/**
 * 主ViewModel
 */
class MainViewModel(application: Application) : AndroidViewModel(application) {
    
    private val repository = DataRepository.getInstance(application)
    
    // 分组数据
    private val _groups = MutableLiveData<List<String>>()
    val groups: LiveData<List<String>> = _groups
    
    // 设备数据
    private val _devices = MutableLiveData<List<Device>>()
    val devices: LiveData<List<Device>> = _devices
    
    // 当前选中的分组
    private val _currentGroup = MutableLiveData<String>("all")
    val currentGroup: LiveData<String> = _currentGroup
    
    // 搜索查询
    private val _searchQuery = MutableLiveData<String>("")
    val searchQuery: LiveData<String> = _searchQuery
    
    // 过滤后的设备列表
    val filteredDevices = MediatorLiveData<List<Device>>().apply {
        addSource(_devices) { updateFilteredDevices() }
        addSource(_currentGroup) { updateFilteredDevices() }
        addSource(_searchQuery) { updateFilteredDevices() }
    }
    
    // 统计数据
    val totalDevices: LiveData<Int> = _devices.map { it.size }
    val totalGroups: LiveData<Int> = _groups.map { it.size }
    val indoorDevices: LiveData<Int> = _devices.map { devices ->
        devices.count { it.type == DeviceType.INDOOR }
    }
    val standardDevices: LiveData<Int> = _devices.map { devices ->
        devices.count { it.type == DeviceType.STANDARD }
    }
    
    // 事件
    private val _showAddDeviceDialog = MutableLiveData<Boolean>()
    val showAddDeviceDialog: LiveData<Boolean> = _showAddDeviceDialog
    
    private val _showEditDeviceDialog = MutableLiveData<Device?>()
    val showEditDeviceDialog: LiveData<Device?> = _showEditDeviceDialog
    
    private val _showDeleteDeviceDialog = MutableLiveData<Device?>()
    val showDeleteDeviceDialog: LiveData<Device?> = _showDeleteDeviceDialog
    
    private val _showAddGroupDialog = MutableLiveData<Boolean>()
    val showAddGroupDialog: LiveData<Boolean> = _showAddGroupDialog
    
    private val _showEditGroupDialog = MutableLiveData<String?>()
    val showEditGroupDialog: LiveData<String?> = _showEditGroupDialog
    
    private val _showDeleteGroupDialog = MutableLiveData<String?>()
    val showDeleteGroupDialog: LiveData<String?> = _showDeleteGroupDialog
    
    init {
        loadData()
    }
    
    private fun loadData() {
        _groups.value = repository.groups
        _devices.value = repository.devices
    }
    
    private fun updateFilteredDevices() {
        val devices = _devices.value ?: return
        val group = _currentGroup.value ?: "all"
        val query = _searchQuery.value ?: ""
        
        val filtered = if (query.isBlank()) {
            repository.getDevices(group)
        } else {
            repository.searchDevices(query, group)
        }
        
        filteredDevices.value = filtered
    }
    
    // 分组操作
    fun setCurrentGroup(group: String) {
        _currentGroup.value = group
    }
    
    fun addGroup(name: String): Boolean {
        val success = repository.addGroup(name)
        if (success) {
            loadData()
        }
        return success
    }
    
    fun editGroup(oldName: String, newName: String): Boolean {
        val success = repository.editGroup(oldName, newName)
        if (success) {
            loadData()
            // 如果编辑的是当前分组，更新当前分组
            if (_currentGroup.value == oldName) {
                _currentGroup.value = newName
            }
        }
        return success
    }
    
    fun deleteGroup(name: String): Boolean {
        val success = repository.deleteGroup(name)
        if (success) {
            loadData()
            // 如果删除的是当前分组，切换到"全部设备"
            if (_currentGroup.value == name) {
                _currentGroup.value = "all"
            }
        }
        return success
    }
    
    fun reorderGroups(fromIndex: Int, toIndex: Int): Boolean {
        val success = repository.reorderGroups(fromIndex, toIndex)
        if (success) {
            loadData()
        }
        return success
    }
    
    // 设备操作
    fun addDevice(device: Device): Device? {
        val newDevice = repository.addDevice(device)
        loadData()
        return newDevice
    }
    
    fun updateDevice(id: String, device: Device): Device? {
        val updatedDevice = repository.updateDevice(id, device)
        if (updatedDevice != null) {
            loadData()
        }
        return updatedDevice
    }
    
    fun deleteDevice(id: String): Boolean {
        val success = repository.deleteDevice(id)
        if (success) {
            loadData()
        }
        return success
    }
    
    fun getDevice(id: String): Device? {
        return repository.getDevice(id)
    }
    
    // 搜索
    fun setSearchQuery(query: String) {
        _searchQuery.value = query
    }
    
    // 统计
    fun getGroupDeviceCount(groupName: String): Int {
        return repository.getGroupDeviceCount(groupName)
    }
    
    fun getGroupStatistics(): List<GroupStatistic> {
        return _groups.value?.map { group ->
            val devices = repository.getDevices(group)
            val indoorCount = devices.count { it.type == DeviceType.INDOOR }
            val standardCount = devices.count { it.type == DeviceType.STANDARD }
            GroupStatistic(group, devices.size, indoorCount, standardCount)
        } ?: emptyList()
    }
    
    // 对话框事件
    fun showAddDeviceDialog() {
        _showAddDeviceDialog.value = true
    }
    
    fun hideAddDeviceDialog() {
        _showAddDeviceDialog.value = false
    }
    
    fun editDevice(device: Device) {
        _showEditDeviceDialog.value = device
    }
    
    fun hideEditDeviceDialog() {
        _showEditDeviceDialog.value = null
    }
    
    fun showDeleteDeviceDialog(device: Device) {
        _showDeleteDeviceDialog.value = device
    }
    
    fun hideDeleteDeviceDialog() {
        _showDeleteDeviceDialog.value = null
    }
    
    fun showAddGroupDialog() {
        _showAddGroupDialog.value = true
    }
    
    fun hideAddGroupDialog() {
        _showAddGroupDialog.value = false
    }
    
    fun editGroup(groupName: String) {
        _showEditGroupDialog.value = groupName
    }
    
    fun hideEditGroupDialog() {
        _showEditGroupDialog.value = null
    }
    
    fun showDeleteGroupDialog(groupName: String) {
        _showDeleteGroupDialog.value = groupName
    }
    
    fun hideDeleteGroupDialog() {
        _showDeleteGroupDialog.value = null
    }
}

/**
 * 分组统计数据类
 */
data class GroupStatistic(
    val groupName: String,
    val totalDevices: Int,
    val indoorDevices: Int,
    val standardDevices: Int
)
