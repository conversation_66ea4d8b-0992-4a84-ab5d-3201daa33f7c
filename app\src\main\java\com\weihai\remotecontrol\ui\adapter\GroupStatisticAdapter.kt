package com.weihai.remotecontrol.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.weihai.remotecontrol.R
import com.weihai.remotecontrol.databinding.ItemGroupStatisticBinding
import com.weihai.remotecontrol.ui.viewmodel.GroupStatistic

/**
 * 分组统计适配器
 */
class GroupStatisticAdapter : ListAdapter<GroupStatistic, GroupStatisticAdapter.ViewHolder>(GroupStatisticDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemGroupStatisticBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    class ViewHolder(
        private val binding: ItemGroupStatisticBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(statistic: GroupStatistic) {
            binding.apply {
                tvGroupName.text = statistic.groupName
                tvDeviceCount.text = root.context.getString(R.string.device_count, statistic.totalDevices)
                tvIndoorCount.text = statistic.indoorDevices.toString()
                tvStandardCount.text = statistic.standardDevices.toString()
            }
        }
    }
}

/**
 * 分组统计差异比较器
 */
class GroupStatisticDiffCallback : DiffUtil.ItemCallback<GroupStatistic>() {
    override fun areItemsTheSame(oldItem: GroupStatistic, newItem: GroupStatistic): Boolean {
        return oldItem.groupName == newItem.groupName
    }

    override fun areContentsTheSame(oldItem: GroupStatistic, newItem: GroupStatistic): Boolean {
        return oldItem == newItem
    }
}
