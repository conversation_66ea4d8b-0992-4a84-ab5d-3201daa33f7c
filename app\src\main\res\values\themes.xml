<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.RemoteControl" parent="Theme.Material3.DayNight.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_variant</item>
        <item name="colorSecondary">@color/secondary</item>
        <item name="colorSecondaryVariant">@color/secondary_variant</item>
        <item name="android:colorBackground">@color/gray_50</item>
        <item name="colorSurface">@color/white</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="colorOnBackground">@color/dark</item>
        <item name="colorOnSurface">@color/dark</item>
        <item name="android:statusBarColor">@color/white</item>
        <item name="android:windowLightStatusBar">true</item>
    </style>
    
    <!-- 卡片样式 -->
    <style name="CardStyle">
        <item name="android:layout_margin">8dp</item>
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">2dp</item>
        <item name="android:background">@color/white</item>
    </style>
    
    <!-- 按钮样式 -->
    <style name="PrimaryButton" parent="Widget.Material3.Button">
        <item name="android:textColor">@color/white</item>
        <item name="backgroundTint">@color/primary</item>
        <item name="cornerRadius">8dp</item>
    </style>
    
    <style name="SecondaryButton" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:textColor">@color/gray_700</item>
        <item name="strokeColor">@color/gray_300</item>
        <item name="cornerRadius">8dp</item>
    </style>
    
    <!-- 输入框样式 -->
    <style name="InputStyle" parent="Widget.Material3.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">@color/primary</item>
        <item name="boxCornerRadiusTopStart">8dp</item>
        <item name="boxCornerRadiusTopEnd">8dp</item>
        <item name="boxCornerRadiusBottomStart">8dp</item>
        <item name="boxCornerRadiusBottomEnd">8dp</item>
    </style>
    
    <!-- 标签样式 -->
    <style name="ChipStyle" parent="Widget.Material3.Chip.Assist">
        <item name="chipCornerRadius">16dp</item>
        <item name="android:textSize">12sp</item>
    </style>
    
    <!-- 分组标签样式 -->
    <style name="GroupChip" parent="ChipStyle">
        <item name="chipBackgroundColor">@color/gray_100</item>
        <item name="android:textColor">@color/gray_600</item>
    </style>
    
    <!-- 设备类型标签样式 -->
    <style name="IndoorChip" parent="ChipStyle">
        <item name="chipBackgroundColor">@color/indoor_bg</item>
        <item name="android:textColor">@color/indoor_text</item>
    </style>
    
    <style name="StandardChip" parent="ChipStyle">
        <item name="chipBackgroundColor">@color/standard_bg</item>
        <item name="android:textColor">@color/standard_text</item>
    </style>
</resources>
