  R android  layout 	android.R  simple_dropdown_item_1line android.R.layout  Application android.app  Dialog android.app  ActivityMainBinding android.app.Activity  AddDeviceDialog android.app.Activity  AddGroupDialog android.app.Activity  Bundle android.app.Activity  CharSequence android.app.Activity  DeleteConfirmDialog android.app.Activity  DevicesFragment android.app.Activity  EditDeviceDialog android.app.Activity  EditGroupDialog android.app.Activity  Editable android.app.Activity  Fragment android.app.Activity  FragmentStateAdapter android.app.Activity  IllegalArgumentException android.app.Activity  Int android.app.Activity  
MainViewModel android.app.Activity  R android.app.Activity  StatisticsFragment android.app.Activity  TextWatcher android.app.Activity  getValue android.app.Activity  let android.app.Activity  observeViewModel android.app.Activity  onCreate android.app.Activity  provideDelegate android.app.Activity  setContentView android.app.Activity  setupBottomNavigation android.app.Activity  setupClickListeners android.app.Activity  setupSearchView android.app.Activity  setupViewPager android.app.Activity  	viewModel android.app.Activity  
viewModels android.app.Activity  ClipData android.content  ClipboardManager android.content  Context android.content  DialogInterface android.content  SharedPreferences android.content  newPlainText android.content.ClipData  setPrimaryClip  android.content.ClipboardManager  ActivityMainBinding android.content.Context  AddDeviceDialog android.content.Context  AddGroupDialog android.content.Context  Bundle android.content.Context  CLIPBOARD_SERVICE android.content.Context  CharSequence android.content.Context  DeleteConfirmDialog android.content.Context  DevicesFragment android.content.Context  EditDeviceDialog android.content.Context  EditGroupDialog android.content.Context  Editable android.content.Context  Fragment android.content.Context  FragmentStateAdapter android.content.Context  IllegalArgumentException android.content.Context  Int android.content.Context  MODE_PRIVATE android.content.Context  
MainViewModel android.content.Context  R android.content.Context  StatisticsFragment android.content.Context  TextWatcher android.content.Context  applicationContext android.content.Context  getAPPLICATIONContext android.content.Context  getApplicationContext android.content.Context  getSharedPreferences android.content.Context  	getString android.content.Context  getSystemService android.content.Context  getValue android.content.Context  let android.content.Context  observeViewModel android.content.Context  onCreate android.content.Context  provideDelegate android.content.Context  setApplicationContext android.content.Context  setContentView android.content.Context  setupBottomNavigation android.content.Context  setupClickListeners android.content.Context  setupSearchView android.content.Context  setupViewPager android.content.Context  	viewModel android.content.Context  
viewModels android.content.Context  ActivityMainBinding android.content.ContextWrapper  AddDeviceDialog android.content.ContextWrapper  AddGroupDialog android.content.ContextWrapper  Bundle android.content.ContextWrapper  CharSequence android.content.ContextWrapper  DeleteConfirmDialog android.content.ContextWrapper  DevicesFragment android.content.ContextWrapper  EditDeviceDialog android.content.ContextWrapper  EditGroupDialog android.content.ContextWrapper  Editable android.content.ContextWrapper  Fragment android.content.ContextWrapper  FragmentStateAdapter android.content.ContextWrapper  IllegalArgumentException android.content.ContextWrapper  Int android.content.ContextWrapper  
MainViewModel android.content.ContextWrapper  R android.content.ContextWrapper  StatisticsFragment android.content.ContextWrapper  TextWatcher android.content.ContextWrapper  getValue android.content.ContextWrapper  let android.content.ContextWrapper  observeViewModel android.content.ContextWrapper  onCreate android.content.ContextWrapper  provideDelegate android.content.ContextWrapper  setContentView android.content.ContextWrapper  setupBottomNavigation android.content.ContextWrapper  setupClickListeners android.content.ContextWrapper  setupSearchView android.content.ContextWrapper  setupViewPager android.content.ContextWrapper  	viewModel android.content.ContextWrapper  
viewModels android.content.ContextWrapper  <SAM-CONSTRUCTOR> /android.content.DialogInterface.OnClickListener  Editor !android.content.SharedPreferences  edit !android.content.SharedPreferences  	getString !android.content.SharedPreferences  KEY_DEVICES (android.content.SharedPreferences.Editor  
KEY_GROUPS (android.content.SharedPreferences.Editor  _devices (android.content.SharedPreferences.Editor  _groups (android.content.SharedPreferences.Editor  apply (android.content.SharedPreferences.Editor  getAPPLY (android.content.SharedPreferences.Editor  getApply (android.content.SharedPreferences.Editor  getGSON (android.content.SharedPreferences.Editor  getGson (android.content.SharedPreferences.Editor  get_devices (android.content.SharedPreferences.Editor  
get_groups (android.content.SharedPreferences.Editor  gson (android.content.SharedPreferences.Editor  	putString (android.content.SharedPreferences.Editor  Bundle 
android.os  Editable android.text  TextWatcher android.text  setPrimaryClip android.text.ClipboardManager  getISNullOrBlank android.text.Editable  getIsNullOrBlank android.text.Editable  getTOString android.text.Editable  getToString android.text.Editable  
isNullOrBlank android.text.Editable  toString android.text.Editable  LayoutInflater android.view  MenuItem android.view  View android.view  	ViewGroup android.view  ActivityMainBinding  android.view.ContextThemeWrapper  AddDeviceDialog  android.view.ContextThemeWrapper  AddGroupDialog  android.view.ContextThemeWrapper  Bundle  android.view.ContextThemeWrapper  CharSequence  android.view.ContextThemeWrapper  DeleteConfirmDialog  android.view.ContextThemeWrapper  DevicesFragment  android.view.ContextThemeWrapper  EditDeviceDialog  android.view.ContextThemeWrapper  EditGroupDialog  android.view.ContextThemeWrapper  Editable  android.view.ContextThemeWrapper  Fragment  android.view.ContextThemeWrapper  FragmentStateAdapter  android.view.ContextThemeWrapper  IllegalArgumentException  android.view.ContextThemeWrapper  Int  android.view.ContextThemeWrapper  
MainViewModel  android.view.ContextThemeWrapper  R  android.view.ContextThemeWrapper  StatisticsFragment  android.view.ContextThemeWrapper  TextWatcher  android.view.ContextThemeWrapper  getValue  android.view.ContextThemeWrapper  let  android.view.ContextThemeWrapper  observeViewModel  android.view.ContextThemeWrapper  onCreate  android.view.ContextThemeWrapper  provideDelegate  android.view.ContextThemeWrapper  setContentView  android.view.ContextThemeWrapper  setupBottomNavigation  android.view.ContextThemeWrapper  setupClickListeners  android.view.ContextThemeWrapper  setupSearchView  android.view.ContextThemeWrapper  setupViewPager  android.view.ContextThemeWrapper  	viewModel  android.view.ContextThemeWrapper  
viewModels  android.view.ContextThemeWrapper  from android.view.LayoutInflater  	getITEMId android.view.MenuItem  	getItemId android.view.MenuItem  itemId android.view.MenuItem  	setItemId android.view.MenuItem  GONE android.view.View  VISIBLE android.view.View  addTextChangedListener android.view.View  addView android.view.View  apply android.view.View  findViewById android.view.View  removeAllViews android.view.View  	selectAll android.view.View  
setAdapter android.view.View  setChipBackgroundColorResource android.view.View  setChipIconResource android.view.View  setOnCheckedStateChangeListener android.view.View  setOnClickListener android.view.View  setOnItemSelectedListener android.view.View  
setPadding android.view.View  setText android.view.View  setTextColor android.view.View  <SAM-CONSTRUCTOR> !android.view.View.OnClickListener  addView android.view.ViewGroup  apply android.view.ViewGroup  context android.view.ViewGroup  findViewById android.view.ViewGroup  
getCONTEXT android.view.ViewGroup  
getContext android.view.ViewGroup  removeAllViews android.view.ViewGroup  
setContext android.view.ViewGroup  setOnCheckedStateChangeListener android.view.ViewGroup  setOnItemSelectedListener android.view.ViewGroup  ArrayAdapter android.widget  EditText android.widget  ImageButton android.widget  LinearLayout android.widget  
ScrollView android.widget  TextView android.widget  Toast android.widget  getTEXT #android.widget.AutoCompleteTextView  getText #android.widget.AutoCompleteTextView  
setAdapter #android.widget.AutoCompleteTextView  setText #android.widget.AutoCompleteTextView  text #android.widget.AutoCompleteTextView  apply android.widget.Button  setChipBackgroundColorResource android.widget.Button  setChipIconResource android.widget.Button  setOnClickListener android.widget.Button  setTextColor android.widget.Button  apply android.widget.CheckBox  setChipBackgroundColorResource android.widget.CheckBox  setChipIconResource android.widget.CheckBox  setTextColor android.widget.CheckBox  apply android.widget.CompoundButton  setChipBackgroundColorResource android.widget.CompoundButton  setChipIconResource android.widget.CompoundButton  setTextColor android.widget.CompoundButton  R android.widget.EditText  addTextChangedListener android.widget.EditText  apply android.widget.EditText  getAPPLY android.widget.EditText  getApply android.widget.EditText  getGETString android.widget.EditText  getGetString android.widget.EditText  getHINT android.widget.EditText  getHint android.widget.EditText  getORIGINALName android.widget.EditText  getOriginalName android.widget.EditText  	getString android.widget.EditText  getTEXT android.widget.EditText  getText android.widget.EditText  hint android.widget.EditText  originalName android.widget.EditText  	selectAll android.widget.EditText  
setAdapter android.widget.EditText  setHint android.widget.EditText  
setPadding android.widget.EditText  setText android.widget.EditText  text android.widget.EditText  setOnItemSelectedListener android.widget.FrameLayout  setOnClickListener android.widget.ImageButton  setOnClickListener android.widget.ImageView  context android.widget.LinearLayout  
getCONTEXT android.widget.LinearLayout  
getContext android.widget.LinearLayout  
getVISIBILITY android.widget.LinearLayout  
getVisibility android.widget.LinearLayout  
setContext android.widget.LinearLayout  
setVisibility android.widget.LinearLayout  
visibility android.widget.LinearLayout  addTextChangedListener android.widget.TextView  apply android.widget.TextView  getTEXT android.widget.TextView  getText android.widget.TextView  
getVISIBILITY android.widget.TextView  
getVisibility android.widget.TextView  	selectAll android.widget.TextView  
setAdapter android.widget.TextView  setChipBackgroundColorResource android.widget.TextView  setChipIconResource android.widget.TextView  setOnClickListener android.widget.TextView  
setPadding android.widget.TextView  setText android.widget.TextView  setTextColor android.widget.TextView  
setVisibility android.widget.TextView  text android.widget.TextView  
visibility android.widget.TextView  LENGTH_SHORT android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  
viewModels androidx.activity  ActivityMainBinding #androidx.activity.ComponentActivity  AddDeviceDialog #androidx.activity.ComponentActivity  AddGroupDialog #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  CharSequence #androidx.activity.ComponentActivity  DeleteConfirmDialog #androidx.activity.ComponentActivity  DevicesFragment #androidx.activity.ComponentActivity  EditDeviceDialog #androidx.activity.ComponentActivity  EditGroupDialog #androidx.activity.ComponentActivity  Editable #androidx.activity.ComponentActivity  Fragment #androidx.activity.ComponentActivity  FragmentStateAdapter #androidx.activity.ComponentActivity  IllegalArgumentException #androidx.activity.ComponentActivity  Int #androidx.activity.ComponentActivity  
MainViewModel #androidx.activity.ComponentActivity  R #androidx.activity.ComponentActivity  StatisticsFragment #androidx.activity.ComponentActivity  TextWatcher #androidx.activity.ComponentActivity  getValue #androidx.activity.ComponentActivity  let #androidx.activity.ComponentActivity  observeViewModel #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  provideDelegate #androidx.activity.ComponentActivity  setContentView #androidx.activity.ComponentActivity  setupBottomNavigation #androidx.activity.ComponentActivity  setupClickListeners #androidx.activity.ComponentActivity  setupSearchView #androidx.activity.ComponentActivity  setupViewPager #androidx.activity.ComponentActivity  	viewModel #androidx.activity.ComponentActivity  
viewModels #androidx.activity.ComponentActivity  AlertDialog androidx.appcompat.app  AppCompatActivity androidx.appcompat.app  Builder "androidx.appcompat.app.AlertDialog  create *androidx.appcompat.app.AlertDialog.Builder  setIcon *androidx.appcompat.app.AlertDialog.Builder  
setMessage *androidx.appcompat.app.AlertDialog.Builder  setNegativeButton *androidx.appcompat.app.AlertDialog.Builder  setPositiveButton *androidx.appcompat.app.AlertDialog.Builder  setTitle *androidx.appcompat.app.AlertDialog.Builder  setView *androidx.appcompat.app.AlertDialog.Builder  ActivityMainBinding (androidx.appcompat.app.AppCompatActivity  AddDeviceDialog (androidx.appcompat.app.AppCompatActivity  AddGroupDialog (androidx.appcompat.app.AppCompatActivity  Bundle (androidx.appcompat.app.AppCompatActivity  CharSequence (androidx.appcompat.app.AppCompatActivity  DeleteConfirmDialog (androidx.appcompat.app.AppCompatActivity  DevicesFragment (androidx.appcompat.app.AppCompatActivity  EditDeviceDialog (androidx.appcompat.app.AppCompatActivity  EditGroupDialog (androidx.appcompat.app.AppCompatActivity  Editable (androidx.appcompat.app.AppCompatActivity  Fragment (androidx.appcompat.app.AppCompatActivity  FragmentStateAdapter (androidx.appcompat.app.AppCompatActivity  IllegalArgumentException (androidx.appcompat.app.AppCompatActivity  Int (androidx.appcompat.app.AppCompatActivity  
MainViewModel (androidx.appcompat.app.AppCompatActivity  R (androidx.appcompat.app.AppCompatActivity  StatisticsFragment (androidx.appcompat.app.AppCompatActivity  TextWatcher (androidx.appcompat.app.AppCompatActivity  getValue (androidx.appcompat.app.AppCompatActivity  let (androidx.appcompat.app.AppCompatActivity  observeViewModel (androidx.appcompat.app.AppCompatActivity  onCreate (androidx.appcompat.app.AppCompatActivity  provideDelegate (androidx.appcompat.app.AppCompatActivity  setContentView (androidx.appcompat.app.AppCompatActivity  setupBottomNavigation (androidx.appcompat.app.AppCompatActivity  setupClickListeners (androidx.appcompat.app.AppCompatActivity  setupSearchView (androidx.appcompat.app.AppCompatActivity  setupViewPager (androidx.appcompat.app.AppCompatActivity  	viewModel (androidx.appcompat.app.AppCompatActivity  
viewModels (androidx.appcompat.app.AppCompatActivity  apply +androidx.appcompat.widget.AppCompatCheckBox  setChipBackgroundColorResource +androidx.appcompat.widget.AppCompatCheckBox  setChipIconResource +androidx.appcompat.widget.AppCompatCheckBox  setTextColor +androidx.appcompat.widget.AppCompatCheckBox  addTextChangedListener +androidx.appcompat.widget.AppCompatEditText  setText +androidx.appcompat.widget.AppCompatEditText  ConstraintLayout  androidx.constraintlayout.widget  ActivityMainBinding #androidx.core.app.ComponentActivity  AddDeviceDialog #androidx.core.app.ComponentActivity  AddGroupDialog #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  CharSequence #androidx.core.app.ComponentActivity  DeleteConfirmDialog #androidx.core.app.ComponentActivity  DevicesFragment #androidx.core.app.ComponentActivity  EditDeviceDialog #androidx.core.app.ComponentActivity  EditGroupDialog #androidx.core.app.ComponentActivity  Editable #androidx.core.app.ComponentActivity  Fragment #androidx.core.app.ComponentActivity  FragmentStateAdapter #androidx.core.app.ComponentActivity  IllegalArgumentException #androidx.core.app.ComponentActivity  Int #androidx.core.app.ComponentActivity  
MainViewModel #androidx.core.app.ComponentActivity  R #androidx.core.app.ComponentActivity  StatisticsFragment #androidx.core.app.ComponentActivity  TextWatcher #androidx.core.app.ComponentActivity  getValue #androidx.core.app.ComponentActivity  let #androidx.core.app.ComponentActivity  observeViewModel #androidx.core.app.ComponentActivity  onCreate #androidx.core.app.ComponentActivity  provideDelegate #androidx.core.app.ComponentActivity  setContentView #androidx.core.app.ComponentActivity  setupBottomNavigation #androidx.core.app.ComponentActivity  setupClickListeners #androidx.core.app.ComponentActivity  setupSearchView #androidx.core.app.ComponentActivity  setupViewPager #androidx.core.app.ComponentActivity  	viewModel #androidx.core.app.ComponentActivity  
viewModels #androidx.core.app.ComponentActivity  
ContextCompat androidx.core.content  getColor #androidx.core.content.ContextCompat  DialogFragment androidx.fragment.app  Fragment androidx.fragment.app  FragmentManager androidx.fragment.app  activityViewModels androidx.fragment.app  AddDeviceDialog $androidx.fragment.app.DialogFragment  AddGroupDialog $androidx.fragment.app.DialogFragment  AlertDialog $androidx.fragment.app.DialogFragment  ArrayAdapter $androidx.fragment.app.DialogFragment  Boolean $androidx.fragment.app.DialogFragment  Bundle $androidx.fragment.app.DialogFragment  DataRepository $androidx.fragment.app.DialogFragment  DeleteConfirmDialog $androidx.fragment.app.DialogFragment  Device $androidx.fragment.app.DialogFragment  
DeviceType $androidx.fragment.app.DialogFragment  Dialog $androidx.fragment.app.DialogFragment  DialogAddDeviceBinding $androidx.fragment.app.DialogFragment  EditDeviceDialog $androidx.fragment.app.DialogFragment  EditGroupDialog $androidx.fragment.app.DialogFragment  EditText $androidx.fragment.app.DialogFragment  FragmentManager $androidx.fragment.app.DialogFragment  R $androidx.fragment.app.DialogFragment  String $androidx.fragment.app.DialogFragment  Unit $androidx.fragment.app.DialogFragment  android $androidx.fragment.app.DialogFragment  apply $androidx.fragment.app.DialogFragment  createDevice $androidx.fragment.app.DialogFragment  dismiss $androidx.fragment.app.DialogFragment  	getString $androidx.fragment.app.DialogFragment  invoke $androidx.fragment.app.DialogFragment  
isNotBlank $androidx.fragment.app.DialogFragment  
isNotEmpty $androidx.fragment.app.DialogFragment  
isNullOrBlank $androidx.fragment.app.DialogFragment  let $androidx.fragment.app.DialogFragment  listOf $androidx.fragment.app.DialogFragment  
onDestroyView $androidx.fragment.app.DialogFragment  originalName $androidx.fragment.app.DialogFragment  populateFields $androidx.fragment.app.DialogFragment  requireContext $androidx.fragment.app.DialogFragment  setupClickListeners $androidx.fragment.app.DialogFragment  setupDropdowns $androidx.fragment.app.DialogFragment  show $androidx.fragment.app.DialogFragment  toString $androidx.fragment.app.DialogFragment  trim $androidx.fragment.app.DialogFragment  
validateInput $androidx.fragment.app.DialogFragment  AddDeviceDialog androidx.fragment.app.Fragment  AddGroupDialog androidx.fragment.app.Fragment  AlertDialog androidx.fragment.app.Fragment  ArrayAdapter androidx.fragment.app.Fragment  Boolean androidx.fragment.app.Fragment  Bundle androidx.fragment.app.Fragment  Chip androidx.fragment.app.Fragment  DataRepository androidx.fragment.app.Fragment  DeleteConfirmDialog androidx.fragment.app.Fragment  Device androidx.fragment.app.Fragment  
DeviceAdapter androidx.fragment.app.Fragment  
DeviceType androidx.fragment.app.Fragment  Dialog androidx.fragment.app.Fragment  DialogAddDeviceBinding androidx.fragment.app.Fragment  EditDeviceDialog androidx.fragment.app.Fragment  EditGroupDialog androidx.fragment.app.Fragment  EditText androidx.fragment.app.Fragment  FragmentDevicesBinding androidx.fragment.app.Fragment  FragmentManager androidx.fragment.app.Fragment  FragmentStatisticsBinding androidx.fragment.app.Fragment  GridLayoutManager androidx.fragment.app.Fragment  GroupStatisticAdapter androidx.fragment.app.Fragment  LayoutInflater androidx.fragment.app.Fragment  LinearLayoutManager androidx.fragment.app.Fragment  List androidx.fragment.app.Fragment  
MainViewModel androidx.fragment.app.Fragment  R androidx.fragment.app.Fragment  String androidx.fragment.app.Fragment  Unit androidx.fragment.app.Fragment  View androidx.fragment.app.Fragment  	ViewGroup androidx.fragment.app.Fragment  activityViewModels androidx.fragment.app.Fragment  android androidx.fragment.app.Fragment  apply androidx.fragment.app.Fragment  createDevice androidx.fragment.app.Fragment  createGroupChip androidx.fragment.app.Fragment  
deviceAdapter androidx.fragment.app.Fragment  dismiss androidx.fragment.app.Fragment  	getString androidx.fragment.app.Fragment  getValue androidx.fragment.app.Fragment  groupStatisticAdapter androidx.fragment.app.Fragment  invoke androidx.fragment.app.Fragment  
isNotBlank androidx.fragment.app.Fragment  
isNotEmpty androidx.fragment.app.Fragment  
isNullOrBlank androidx.fragment.app.Fragment  let androidx.fragment.app.Fragment  listOf androidx.fragment.app.Fragment  observeViewModel androidx.fragment.app.Fragment  
onDestroyView androidx.fragment.app.Fragment  
onViewCreated androidx.fragment.app.Fragment  originalName androidx.fragment.app.Fragment  populateFields androidx.fragment.app.Fragment  provideDelegate androidx.fragment.app.Fragment  requireContext androidx.fragment.app.Fragment  setupClickListeners androidx.fragment.app.Fragment  setupDropdowns androidx.fragment.app.Fragment  setupGroupTabs androidx.fragment.app.Fragment  setupRecyclerView androidx.fragment.app.Fragment  show androidx.fragment.app.Fragment  toString androidx.fragment.app.Fragment  trim androidx.fragment.app.Fragment  updateDeviceList androidx.fragment.app.Fragment  updateEmptyState androidx.fragment.app.Fragment  updateGroupStatistics androidx.fragment.app.Fragment  updateGroupTabs androidx.fragment.app.Fragment  
validateInput androidx.fragment.app.Fragment  ActivityMainBinding &androidx.fragment.app.FragmentActivity  AddDeviceDialog &androidx.fragment.app.FragmentActivity  AddGroupDialog &androidx.fragment.app.FragmentActivity  Bundle &androidx.fragment.app.FragmentActivity  CharSequence &androidx.fragment.app.FragmentActivity  DeleteConfirmDialog &androidx.fragment.app.FragmentActivity  DevicesFragment &androidx.fragment.app.FragmentActivity  EditDeviceDialog &androidx.fragment.app.FragmentActivity  EditGroupDialog &androidx.fragment.app.FragmentActivity  Editable &androidx.fragment.app.FragmentActivity  Fragment &androidx.fragment.app.FragmentActivity  FragmentStateAdapter &androidx.fragment.app.FragmentActivity  IllegalArgumentException &androidx.fragment.app.FragmentActivity  Int &androidx.fragment.app.FragmentActivity  
MainViewModel &androidx.fragment.app.FragmentActivity  R &androidx.fragment.app.FragmentActivity  StatisticsFragment &androidx.fragment.app.FragmentActivity  TextWatcher &androidx.fragment.app.FragmentActivity  getValue &androidx.fragment.app.FragmentActivity  let &androidx.fragment.app.FragmentActivity  observeViewModel &androidx.fragment.app.FragmentActivity  onCreate &androidx.fragment.app.FragmentActivity  provideDelegate &androidx.fragment.app.FragmentActivity  setContentView &androidx.fragment.app.FragmentActivity  setupBottomNavigation &androidx.fragment.app.FragmentActivity  setupClickListeners &androidx.fragment.app.FragmentActivity  setupSearchView &androidx.fragment.app.FragmentActivity  setupViewPager &androidx.fragment.app.FragmentActivity  	viewModel &androidx.fragment.app.FragmentActivity  
viewModels &androidx.fragment.app.FragmentActivity  AndroidViewModel androidx.lifecycle  LifecycleOwner androidx.lifecycle  LiveData androidx.lifecycle  MediatorLiveData androidx.lifecycle  MutableLiveData androidx.lifecycle  map androidx.lifecycle  Application #androidx.lifecycle.AndroidViewModel  Boolean #androidx.lifecycle.AndroidViewModel  DataRepository #androidx.lifecycle.AndroidViewModel  Device #androidx.lifecycle.AndroidViewModel  
DeviceType #androidx.lifecycle.AndroidViewModel  GroupStatistic #androidx.lifecycle.AndroidViewModel  Int #androidx.lifecycle.AndroidViewModel  List #androidx.lifecycle.AndroidViewModel  LiveData #androidx.lifecycle.AndroidViewModel  MediatorLiveData #androidx.lifecycle.AndroidViewModel  MutableLiveData #androidx.lifecycle.AndroidViewModel  String #androidx.lifecycle.AndroidViewModel  
_currentGroup #androidx.lifecycle.AndroidViewModel  _devices #androidx.lifecycle.AndroidViewModel  _searchQuery #androidx.lifecycle.AndroidViewModel  	addDevice #androidx.lifecycle.AndroidViewModel  addGroup #androidx.lifecycle.AndroidViewModel  apply #androidx.lifecycle.AndroidViewModel  count #androidx.lifecycle.AndroidViewModel  deleteDevice #androidx.lifecycle.AndroidViewModel  deleteGroup #androidx.lifecycle.AndroidViewModel  
editDevice #androidx.lifecycle.AndroidViewModel  	editGroup #androidx.lifecycle.AndroidViewModel  	emptyList #androidx.lifecycle.AndroidViewModel  getGroupDeviceCount #androidx.lifecycle.AndroidViewModel  getGroupStatistics #androidx.lifecycle.AndroidViewModel  hideAddDeviceDialog #androidx.lifecycle.AndroidViewModel  hideAddGroupDialog #androidx.lifecycle.AndroidViewModel  hideDeleteDeviceDialog #androidx.lifecycle.AndroidViewModel  hideDeleteGroupDialog #androidx.lifecycle.AndroidViewModel  hideEditDeviceDialog #androidx.lifecycle.AndroidViewModel  hideEditGroupDialog #androidx.lifecycle.AndroidViewModel  isBlank #androidx.lifecycle.AndroidViewModel  loadData #androidx.lifecycle.AndroidViewModel  map #androidx.lifecycle.AndroidViewModel  setCurrentGroup #androidx.lifecycle.AndroidViewModel  setSearchQuery #androidx.lifecycle.AndroidViewModel  showAddDeviceDialog #androidx.lifecycle.AndroidViewModel  showAddGroupDialog #androidx.lifecycle.AndroidViewModel  showDeleteDeviceDialog #androidx.lifecycle.AndroidViewModel  updateDevice #androidx.lifecycle.AndroidViewModel  updateFilteredDevices #androidx.lifecycle.AndroidViewModel  	addSource androidx.lifecycle.LiveData  apply androidx.lifecycle.LiveData  invoke androidx.lifecycle.LiveData  map androidx.lifecycle.LiveData  observe androidx.lifecycle.LiveData  
_currentGroup #androidx.lifecycle.MediatorLiveData  _devices #androidx.lifecycle.MediatorLiveData  _searchQuery #androidx.lifecycle.MediatorLiveData  	addSource #androidx.lifecycle.MediatorLiveData  apply #androidx.lifecycle.MediatorLiveData  getAPPLY #androidx.lifecycle.MediatorLiveData  getApply #androidx.lifecycle.MediatorLiveData  getUPDATEFilteredDevices #androidx.lifecycle.MediatorLiveData  getUpdateFilteredDevices #androidx.lifecycle.MediatorLiveData  getVALUE #androidx.lifecycle.MediatorLiveData  getValue #androidx.lifecycle.MediatorLiveData  get_currentGroup #androidx.lifecycle.MediatorLiveData  get_devices #androidx.lifecycle.MediatorLiveData  get_searchQuery #androidx.lifecycle.MediatorLiveData  observe #androidx.lifecycle.MediatorLiveData  setValue #androidx.lifecycle.MediatorLiveData  updateFilteredDevices #androidx.lifecycle.MediatorLiveData  value #androidx.lifecycle.MediatorLiveData  	addSource "androidx.lifecycle.MutableLiveData  apply "androidx.lifecycle.MutableLiveData  getMAP "androidx.lifecycle.MutableLiveData  getMap "androidx.lifecycle.MutableLiveData  getVALUE "androidx.lifecycle.MutableLiveData  getValue "androidx.lifecycle.MutableLiveData  map "androidx.lifecycle.MutableLiveData  observe "androidx.lifecycle.MutableLiveData  setValue "androidx.lifecycle.MutableLiveData  value "androidx.lifecycle.MutableLiveData  <SAM-CONSTRUCTOR> androidx.lifecycle.Observer  Application androidx.lifecycle.ViewModel  Boolean androidx.lifecycle.ViewModel  DataRepository androidx.lifecycle.ViewModel  Device androidx.lifecycle.ViewModel  
DeviceType androidx.lifecycle.ViewModel  GroupStatistic androidx.lifecycle.ViewModel  Int androidx.lifecycle.ViewModel  List androidx.lifecycle.ViewModel  LiveData androidx.lifecycle.ViewModel  MediatorLiveData androidx.lifecycle.ViewModel  MutableLiveData androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  
_currentGroup androidx.lifecycle.ViewModel  _devices androidx.lifecycle.ViewModel  _searchQuery androidx.lifecycle.ViewModel  	addDevice androidx.lifecycle.ViewModel  addGroup androidx.lifecycle.ViewModel  apply androidx.lifecycle.ViewModel  count androidx.lifecycle.ViewModel  deleteDevice androidx.lifecycle.ViewModel  deleteGroup androidx.lifecycle.ViewModel  
editDevice androidx.lifecycle.ViewModel  	editGroup androidx.lifecycle.ViewModel  	emptyList androidx.lifecycle.ViewModel  getGroupDeviceCount androidx.lifecycle.ViewModel  getGroupStatistics androidx.lifecycle.ViewModel  hideAddDeviceDialog androidx.lifecycle.ViewModel  hideAddGroupDialog androidx.lifecycle.ViewModel  hideDeleteDeviceDialog androidx.lifecycle.ViewModel  hideDeleteGroupDialog androidx.lifecycle.ViewModel  hideEditDeviceDialog androidx.lifecycle.ViewModel  hideEditGroupDialog androidx.lifecycle.ViewModel  isBlank androidx.lifecycle.ViewModel  loadData androidx.lifecycle.ViewModel  map androidx.lifecycle.ViewModel  setCurrentGroup androidx.lifecycle.ViewModel  setSearchQuery androidx.lifecycle.ViewModel  showAddDeviceDialog androidx.lifecycle.ViewModel  showAddGroupDialog androidx.lifecycle.ViewModel  showDeleteDeviceDialog androidx.lifecycle.ViewModel  updateDevice androidx.lifecycle.ViewModel  updateFilteredDevices androidx.lifecycle.ViewModel  DiffUtil androidx.recyclerview.widget  GridLayoutManager androidx.recyclerview.widget  LinearLayoutManager androidx.recyclerview.widget  ListAdapter androidx.recyclerview.widget  RecyclerView androidx.recyclerview.widget  ItemCallback %androidx.recyclerview.widget.DiffUtil  Boolean 2androidx.recyclerview.widget.DiffUtil.ItemCallback  Device 2androidx.recyclerview.widget.DiffUtil.ItemCallback  GroupStatistic 2androidx.recyclerview.widget.DiffUtil.ItemCallback  ClipData (androidx.recyclerview.widget.ListAdapter  ClipboardManager (androidx.recyclerview.widget.ListAdapter  Context (androidx.recyclerview.widget.ListAdapter  
ContextCompat (androidx.recyclerview.widget.ListAdapter  Device (androidx.recyclerview.widget.ListAdapter  DeviceDiffCallback (androidx.recyclerview.widget.ListAdapter  
DeviceType (androidx.recyclerview.widget.ListAdapter  GroupStatistic (androidx.recyclerview.widget.ListAdapter  GroupStatisticDiffCallback (androidx.recyclerview.widget.ListAdapter  Int (androidx.recyclerview.widget.ListAdapter  ItemDeviceBinding (androidx.recyclerview.widget.ListAdapter  ItemGroupStatisticBinding (androidx.recyclerview.widget.ListAdapter  LayoutInflater (androidx.recyclerview.widget.ListAdapter  R (androidx.recyclerview.widget.ListAdapter  RecyclerView (androidx.recyclerview.widget.ListAdapter  String (androidx.recyclerview.widget.ListAdapter  Toast (androidx.recyclerview.widget.ListAdapter  Unit (androidx.recyclerview.widget.ListAdapter  	ViewGroup (androidx.recyclerview.widget.ListAdapter  
ViewHolder (androidx.recyclerview.widget.ListAdapter  apply (androidx.recyclerview.widget.ListAdapter  copyToClipboard (androidx.recyclerview.widget.ListAdapter  getItem (androidx.recyclerview.widget.ListAdapter  invoke (androidx.recyclerview.widget.ListAdapter  
onDeleteClick (androidx.recyclerview.widget.ListAdapter  onEditClick (androidx.recyclerview.widget.ListAdapter  
submitList (androidx.recyclerview.widget.ListAdapter  Adapter )androidx.recyclerview.widget.RecyclerView  GridLayoutManager )androidx.recyclerview.widget.RecyclerView  
LayoutManager )androidx.recyclerview.widget.RecyclerView  LinearLayoutManager )androidx.recyclerview.widget.RecyclerView  
ViewHolder )androidx.recyclerview.widget.RecyclerView  adapter )androidx.recyclerview.widget.RecyclerView  apply )androidx.recyclerview.widget.RecyclerView  
deviceAdapter )androidx.recyclerview.widget.RecyclerView  
getADAPTER )androidx.recyclerview.widget.RecyclerView  getAPPLY )androidx.recyclerview.widget.RecyclerView  
getAdapter )androidx.recyclerview.widget.RecyclerView  getApply )androidx.recyclerview.widget.RecyclerView  getDEVICEAdapter )androidx.recyclerview.widget.RecyclerView  getDeviceAdapter )androidx.recyclerview.widget.RecyclerView  getGROUPStatisticAdapter )androidx.recyclerview.widget.RecyclerView  getGroupStatisticAdapter )androidx.recyclerview.widget.RecyclerView  getLAYOUTManager )androidx.recyclerview.widget.RecyclerView  getLayoutManager )androidx.recyclerview.widget.RecyclerView  getREQUIREContext )androidx.recyclerview.widget.RecyclerView  getRequireContext )androidx.recyclerview.widget.RecyclerView  
getVISIBILITY )androidx.recyclerview.widget.RecyclerView  
getVisibility )androidx.recyclerview.widget.RecyclerView  groupStatisticAdapter )androidx.recyclerview.widget.RecyclerView  
layoutManager )androidx.recyclerview.widget.RecyclerView  requireContext )androidx.recyclerview.widget.RecyclerView  
setAdapter )androidx.recyclerview.widget.RecyclerView  setLayoutManager )androidx.recyclerview.widget.RecyclerView  
setVisibility )androidx.recyclerview.widget.RecyclerView  
visibility )androidx.recyclerview.widget.RecyclerView  ClipData 1androidx.recyclerview.widget.RecyclerView.Adapter  ClipboardManager 1androidx.recyclerview.widget.RecyclerView.Adapter  Context 1androidx.recyclerview.widget.RecyclerView.Adapter  
ContextCompat 1androidx.recyclerview.widget.RecyclerView.Adapter  Device 1androidx.recyclerview.widget.RecyclerView.Adapter  DeviceDiffCallback 1androidx.recyclerview.widget.RecyclerView.Adapter  
DeviceType 1androidx.recyclerview.widget.RecyclerView.Adapter  DevicesFragment 1androidx.recyclerview.widget.RecyclerView.Adapter  Fragment 1androidx.recyclerview.widget.RecyclerView.Adapter  GroupStatistic 1androidx.recyclerview.widget.RecyclerView.Adapter  GroupStatisticDiffCallback 1androidx.recyclerview.widget.RecyclerView.Adapter  IllegalArgumentException 1androidx.recyclerview.widget.RecyclerView.Adapter  Int 1androidx.recyclerview.widget.RecyclerView.Adapter  ItemDeviceBinding 1androidx.recyclerview.widget.RecyclerView.Adapter  ItemGroupStatisticBinding 1androidx.recyclerview.widget.RecyclerView.Adapter  LayoutInflater 1androidx.recyclerview.widget.RecyclerView.Adapter  R 1androidx.recyclerview.widget.RecyclerView.Adapter  RecyclerView 1androidx.recyclerview.widget.RecyclerView.Adapter  StatisticsFragment 1androidx.recyclerview.widget.RecyclerView.Adapter  String 1androidx.recyclerview.widget.RecyclerView.Adapter  Toast 1androidx.recyclerview.widget.RecyclerView.Adapter  Unit 1androidx.recyclerview.widget.RecyclerView.Adapter  	ViewGroup 1androidx.recyclerview.widget.RecyclerView.Adapter  
ViewHolder 1androidx.recyclerview.widget.RecyclerView.Adapter  apply 1androidx.recyclerview.widget.RecyclerView.Adapter  copyToClipboard 1androidx.recyclerview.widget.RecyclerView.Adapter  getItem 1androidx.recyclerview.widget.RecyclerView.Adapter  invoke 1androidx.recyclerview.widget.RecyclerView.Adapter  
onDeleteClick 1androidx.recyclerview.widget.RecyclerView.Adapter  onEditClick 1androidx.recyclerview.widget.RecyclerView.Adapter  
submitList 1androidx.recyclerview.widget.RecyclerView.Adapter  ClipData 4androidx.recyclerview.widget.RecyclerView.ViewHolder  ClipboardManager 4androidx.recyclerview.widget.RecyclerView.ViewHolder  Context 4androidx.recyclerview.widget.RecyclerView.ViewHolder  
ContextCompat 4androidx.recyclerview.widget.RecyclerView.ViewHolder  Device 4androidx.recyclerview.widget.RecyclerView.ViewHolder  
DeviceType 4androidx.recyclerview.widget.RecyclerView.ViewHolder  GroupStatistic 4androidx.recyclerview.widget.RecyclerView.ViewHolder  ItemDeviceBinding 4androidx.recyclerview.widget.RecyclerView.ViewHolder  ItemGroupStatisticBinding 4androidx.recyclerview.widget.RecyclerView.ViewHolder  R 4androidx.recyclerview.widget.RecyclerView.ViewHolder  String 4androidx.recyclerview.widget.RecyclerView.ViewHolder  Toast 4androidx.recyclerview.widget.RecyclerView.ViewHolder  apply 4androidx.recyclerview.widget.RecyclerView.ViewHolder  bind 4androidx.recyclerview.widget.RecyclerView.ViewHolder  copyToClipboard 4androidx.recyclerview.widget.RecyclerView.ViewHolder  invoke 4androidx.recyclerview.widget.RecyclerView.ViewHolder  
onDeleteClick 4androidx.recyclerview.widget.RecyclerView.ViewHolder  onEditClick 4androidx.recyclerview.widget.RecyclerView.ViewHolder  FragmentStateAdapter androidx.viewpager2.adapter  DevicesFragment 0androidx.viewpager2.adapter.FragmentStateAdapter  Fragment 0androidx.viewpager2.adapter.FragmentStateAdapter  IllegalArgumentException 0androidx.viewpager2.adapter.FragmentStateAdapter  Int 0androidx.viewpager2.adapter.FragmentStateAdapter  StatisticsFragment 0androidx.viewpager2.adapter.FragmentStateAdapter  adapter %androidx.viewpager2.widget.ViewPager2  currentItem %androidx.viewpager2.widget.ViewPager2  
getADAPTER %androidx.viewpager2.widget.ViewPager2  
getAdapter %androidx.viewpager2.widget.ViewPager2  getCURRENTItem %androidx.viewpager2.widget.ViewPager2  getCurrentItem %androidx.viewpager2.widget.ViewPager2  getISUserInputEnabled %androidx.viewpager2.widget.ViewPager2  getIsUserInputEnabled %androidx.viewpager2.widget.ViewPager2  isUserInputEnabled %androidx.viewpager2.widget.ViewPager2  
setAdapter %androidx.viewpager2.widget.ViewPager2  setCurrentItem %androidx.viewpager2.widget.ViewPager2  setUserInputEnabled %androidx.viewpager2.widget.ViewPager2  setOnItemSelectedListener Acom.google.android.material.bottomnavigation.BottomNavigationView  MaterialCardView  com.google.android.material.card  context 1com.google.android.material.card.MaterialCardView  
getCONTEXT 1com.google.android.material.card.MaterialCardView  
getContext 1com.google.android.material.card.MaterialCardView  
setContext 1com.google.android.material.card.MaterialCardView  Chip  com.google.android.material.chip  	ChipGroup  com.google.android.material.chip  R %com.google.android.material.chip.Chip  apply %com.google.android.material.chip.Chip  getAPPLY %com.google.android.material.chip.Chip  getApply %com.google.android.material.chip.Chip  getISCheckable %com.google.android.material.chip.Chip  getISChecked %com.google.android.material.chip.Chip  getIsCheckable %com.google.android.material.chip.Chip  getIsChecked %com.google.android.material.chip.Chip  getTAG %com.google.android.material.chip.Chip  getTEXT %com.google.android.material.chip.Chip  getTag %com.google.android.material.chip.Chip  getText %com.google.android.material.chip.Chip  isCheckable %com.google.android.material.chip.Chip  	isChecked %com.google.android.material.chip.Chip  setCheckable %com.google.android.material.chip.Chip  
setChecked %com.google.android.material.chip.Chip  setChipBackgroundColorResource %com.google.android.material.chip.Chip  setChipIconResource %com.google.android.material.chip.Chip  setTag %com.google.android.material.chip.Chip  setText %com.google.android.material.chip.Chip  setTextColor %com.google.android.material.chip.Chip  tag %com.google.android.material.chip.Chip  text %com.google.android.material.chip.Chip  addView *com.google.android.material.chip.ChipGroup  findViewById *com.google.android.material.chip.ChipGroup  removeAllViews *com.google.android.material.chip.ChipGroup  setOnCheckedStateChangeListener *com.google.android.material.chip.ChipGroup  <SAM-CONSTRUCTOR> Gcom.google.android.material.chip.ChipGroup.OnCheckedStateChangeListener  addView /com.google.android.material.internal.FlowLayout  findViewById /com.google.android.material.internal.FlowLayout  removeAllViews /com.google.android.material.internal.FlowLayout  setOnCheckedStateChangeListener /com.google.android.material.internal.FlowLayout  setOnItemSelectedListener 8com.google.android.material.navigation.NavigationBarView  <SAM-CONSTRUCTOR> Ocom.google.android.material.navigation.NavigationBarView.OnItemSelectedListener  TabLayoutMediator  com.google.android.material.tabs  addTextChangedListener 7com.google.android.material.textfield.TextInputEditText  getTEXT 7com.google.android.material.textfield.TextInputEditText  getText 7com.google.android.material.textfield.TextInputEditText  setText 7com.google.android.material.textfield.TextInputEditText  text 7com.google.android.material.textfield.TextInputEditText  error 5com.google.android.material.textfield.TextInputLayout  getERROR 5com.google.android.material.textfield.TextInputLayout  getError 5com.google.android.material.textfield.TextInputLayout  setError 5com.google.android.material.textfield.TextInputLayout  Gson com.google.gson  fromJson com.google.gson.Gson  toJson com.google.gson.Gson  SerializedName com.google.gson.annotations  	TypeToken com.google.gson.reflect  ActivityMainBinding com.weihai.remotecontrol  AddDeviceDialog com.weihai.remotecontrol  AddGroupDialog com.weihai.remotecontrol  CharSequence com.weihai.remotecontrol  DeleteConfirmDialog com.weihai.remotecontrol  DevicesFragment com.weihai.remotecontrol  EditDeviceDialog com.weihai.remotecontrol  EditGroupDialog com.weihai.remotecontrol  IllegalArgumentException com.weihai.remotecontrol  Int com.weihai.remotecontrol  MainActivity com.weihai.remotecontrol  R com.weihai.remotecontrol  StatisticsFragment com.weihai.remotecontrol  getValue com.weihai.remotecontrol  let com.weihai.remotecontrol  provideDelegate com.weihai.remotecontrol  	viewModel com.weihai.remotecontrol  
viewModels com.weihai.remotecontrol  ActivityMainBinding %com.weihai.remotecontrol.MainActivity  AddDeviceDialog %com.weihai.remotecontrol.MainActivity  AddGroupDialog %com.weihai.remotecontrol.MainActivity  Bundle %com.weihai.remotecontrol.MainActivity  CharSequence %com.weihai.remotecontrol.MainActivity  DeleteConfirmDialog %com.weihai.remotecontrol.MainActivity  DevicesFragment %com.weihai.remotecontrol.MainActivity  EditDeviceDialog %com.weihai.remotecontrol.MainActivity  EditGroupDialog %com.weihai.remotecontrol.MainActivity  Editable %com.weihai.remotecontrol.MainActivity  Fragment %com.weihai.remotecontrol.MainActivity  FragmentStateAdapter %com.weihai.remotecontrol.MainActivity  IllegalArgumentException %com.weihai.remotecontrol.MainActivity  Int %com.weihai.remotecontrol.MainActivity  
MainViewModel %com.weihai.remotecontrol.MainActivity  R %com.weihai.remotecontrol.MainActivity  StatisticsFragment %com.weihai.remotecontrol.MainActivity  TextWatcher %com.weihai.remotecontrol.MainActivity  ViewPagerAdapter %com.weihai.remotecontrol.MainActivity  binding %com.weihai.remotecontrol.MainActivity  getGETValue %com.weihai.remotecontrol.MainActivity  getGetValue %com.weihai.remotecontrol.MainActivity  getLAYOUTInflater %com.weihai.remotecontrol.MainActivity  getLET %com.weihai.remotecontrol.MainActivity  getLayoutInflater %com.weihai.remotecontrol.MainActivity  getLet %com.weihai.remotecontrol.MainActivity  getPROVIDEDelegate %com.weihai.remotecontrol.MainActivity  getProvideDelegate %com.weihai.remotecontrol.MainActivity  getSUPPORTFragmentManager %com.weihai.remotecontrol.MainActivity  getSupportFragmentManager %com.weihai.remotecontrol.MainActivity  
getVIEWModels %com.weihai.remotecontrol.MainActivity  getValue %com.weihai.remotecontrol.MainActivity  
getViewModels %com.weihai.remotecontrol.MainActivity  layoutInflater %com.weihai.remotecontrol.MainActivity  let %com.weihai.remotecontrol.MainActivity  observeViewModel %com.weihai.remotecontrol.MainActivity  provideDelegate %com.weihai.remotecontrol.MainActivity  setContentView %com.weihai.remotecontrol.MainActivity  setLayoutInflater %com.weihai.remotecontrol.MainActivity  setSupportFragmentManager %com.weihai.remotecontrol.MainActivity  setupBottomNavigation %com.weihai.remotecontrol.MainActivity  setupClickListeners %com.weihai.remotecontrol.MainActivity  setupSearchView %com.weihai.remotecontrol.MainActivity  setupViewPager %com.weihai.remotecontrol.MainActivity  supportFragmentManager %com.weihai.remotecontrol.MainActivity  	viewModel %com.weihai.remotecontrol.MainActivity  
viewModels %com.weihai.remotecontrol.MainActivity  DevicesFragment 6com.weihai.remotecontrol.MainActivity.ViewPagerAdapter  Fragment 6com.weihai.remotecontrol.MainActivity.ViewPagerAdapter  IllegalArgumentException 6com.weihai.remotecontrol.MainActivity.ViewPagerAdapter  Int 6com.weihai.remotecontrol.MainActivity.ViewPagerAdapter  StatisticsFragment 6com.weihai.remotecontrol.MainActivity.ViewPagerAdapter  getVIEWModel Hcom.weihai.remotecontrol.MainActivity.setupSearchView.<no name provided>  getViewModel Hcom.weihai.remotecontrol.MainActivity.setupSearchView.<no name provided>  color com.weihai.remotecontrol.R  drawable com.weihai.remotecontrol.R  id com.weihai.remotecontrol.R  string com.weihai.remotecontrol.R  	indoor_bg  com.weihai.remotecontrol.R.color  indoor_text  com.weihai.remotecontrol.R.color  standard_bg  com.weihai.remotecontrol.R.color  
standard_text  com.weihai.remotecontrol.R.color  ic_all_devices #com.weihai.remotecontrol.R.drawable  	ic_delete #com.weihai.remotecontrol.R.drawable  	ic_folder #com.weihai.remotecontrol.R.drawable  nav_devices com.weihai.remotecontrol.R.id  nav_statistics com.weihai.remotecontrol.R.id  	add_group !com.weihai.remotecontrol.R.string  cancel !com.weihai.remotecontrol.R.string  copy_success !com.weihai.remotecontrol.R.string  create !com.weihai.remotecontrol.R.string  delete !com.weihai.remotecontrol.R.string  device_count !com.weihai.remotecontrol.R.string  edit !com.weihai.remotecontrol.R.string  
group_name !com.weihai.remotecontrol.R.string  no_devices_in_group !com.weihai.remotecontrol.R.string  no_search_results !com.weihai.remotecontrol.R.string  required_field !com.weihai.remotecontrol.R.string  save !com.weihai.remotecontrol.R.string  Boolean com.weihai.remotecontrol.data  Context com.weihai.remotecontrol.data  DataRepository com.weihai.remotecontrol.data  Device com.weihai.remotecontrol.data  
DeviceType com.weihai.remotecontrol.data  Gson com.weihai.remotecontrol.data  Int com.weihai.remotecontrol.data  KEY_DEVICES com.weihai.remotecontrol.data  
KEY_GROUPS com.weihai.remotecontrol.data  List com.weihai.remotecontrol.data  
PREFS_NAME com.weihai.remotecontrol.data  STANDARD com.weihai.remotecontrol.data  String com.weihai.remotecontrol.data  System com.weihai.remotecontrol.data  Volatile com.weihai.remotecontrol.data  _devices com.weihai.remotecontrol.data  _groups com.weihai.remotecontrol.data  also com.weihai.remotecontrol.data  apply com.weihai.remotecontrol.data  contains com.weihai.remotecontrol.data  count com.weihai.remotecontrol.data  filter com.weihai.remotecontrol.data  find com.weihai.remotecontrol.data  forEach com.weihai.remotecontrol.data  gson com.weihai.remotecontrol.data  indexOfFirst com.weihai.remotecontrol.data  invoke com.weihai.remotecontrol.data  isBlank com.weihai.remotecontrol.data  
isNotEmpty com.weihai.remotecontrol.data  listOf com.weihai.remotecontrol.data  	lowercase com.weihai.remotecontrol.data  
mutableListOf com.weihai.remotecontrol.data  	removeAll com.weihai.remotecontrol.data  synchronized com.weihai.remotecontrol.data  toList com.weihai.remotecontrol.data  
toMutableList com.weihai.remotecontrol.data  trim com.weihai.remotecontrol.data  values com.weihai.remotecontrol.data  Boolean ,com.weihai.remotecontrol.data.DataRepository  	Companion ,com.weihai.remotecontrol.data.DataRepository  Context ,com.weihai.remotecontrol.data.DataRepository  DataRepository ,com.weihai.remotecontrol.data.DataRepository  Device ,com.weihai.remotecontrol.data.DataRepository  
DeviceType ,com.weihai.remotecontrol.data.DataRepository  Gson ,com.weihai.remotecontrol.data.DataRepository  Int ,com.weihai.remotecontrol.data.DataRepository  KEY_DEVICES ,com.weihai.remotecontrol.data.DataRepository  
KEY_GROUPS ,com.weihai.remotecontrol.data.DataRepository  List ,com.weihai.remotecontrol.data.DataRepository  
PREFS_NAME ,com.weihai.remotecontrol.data.DataRepository  SharedPreferences ,com.weihai.remotecontrol.data.DataRepository  String ,com.weihai.remotecontrol.data.DataRepository  	TypeToken ,com.weihai.remotecontrol.data.DataRepository  Volatile ,com.weihai.remotecontrol.data.DataRepository  _devices ,com.weihai.remotecontrol.data.DataRepository  _groups ,com.weihai.remotecontrol.data.DataRepository  	addDevice ,com.weihai.remotecontrol.data.DataRepository  addGroup ,com.weihai.remotecontrol.data.DataRepository  also ,com.weihai.remotecontrol.data.DataRepository  apply ,com.weihai.remotecontrol.data.DataRepository  contains ,com.weihai.remotecontrol.data.DataRepository  count ,com.weihai.remotecontrol.data.DataRepository  deleteDevice ,com.weihai.remotecontrol.data.DataRepository  deleteGroup ,com.weihai.remotecontrol.data.DataRepository  devices ,com.weihai.remotecontrol.data.DataRepository  	editGroup ,com.weihai.remotecontrol.data.DataRepository  filter ,com.weihai.remotecontrol.data.DataRepository  find ,com.weihai.remotecontrol.data.DataRepository  getALSO ,com.weihai.remotecontrol.data.DataRepository  getAPPLY ,com.weihai.remotecontrol.data.DataRepository  getAlso ,com.weihai.remotecontrol.data.DataRepository  getApply ,com.weihai.remotecontrol.data.DataRepository  getCONTAINS ,com.weihai.remotecontrol.data.DataRepository  getCOUNT ,com.weihai.remotecontrol.data.DataRepository  getContains ,com.weihai.remotecontrol.data.DataRepository  getCount ,com.weihai.remotecontrol.data.DataRepository  	getDevice ,com.weihai.remotecontrol.data.DataRepository  
getDevices ,com.weihai.remotecontrol.data.DataRepository  	getFILTER ,com.weihai.remotecontrol.data.DataRepository  getFIND ,com.weihai.remotecontrol.data.DataRepository  	getFilter ,com.weihai.remotecontrol.data.DataRepository  getFind ,com.weihai.remotecontrol.data.DataRepository  getGroupDeviceCount ,com.weihai.remotecontrol.data.DataRepository  getINDEXOfFirst ,com.weihai.remotecontrol.data.DataRepository  
getISBlank ,com.weihai.remotecontrol.data.DataRepository  
getISNotEmpty ,com.weihai.remotecontrol.data.DataRepository  getIndexOfFirst ,com.weihai.remotecontrol.data.DataRepository  getInstance ,com.weihai.remotecontrol.data.DataRepository  
getIsBlank ,com.weihai.remotecontrol.data.DataRepository  
getIsNotEmpty ,com.weihai.remotecontrol.data.DataRepository  	getLISTOf ,com.weihai.remotecontrol.data.DataRepository  getLOWERCASE ,com.weihai.remotecontrol.data.DataRepository  	getListOf ,com.weihai.remotecontrol.data.DataRepository  getLowercase ,com.weihai.remotecontrol.data.DataRepository  getMUTABLEListOf ,com.weihai.remotecontrol.data.DataRepository  getMutableListOf ,com.weihai.remotecontrol.data.DataRepository  getREMOVEAll ,com.weihai.remotecontrol.data.DataRepository  getRemoveAll ,com.weihai.remotecontrol.data.DataRepository  	getTOList ,com.weihai.remotecontrol.data.DataRepository  getTOMutableList ,com.weihai.remotecontrol.data.DataRepository  getTRIM ,com.weihai.remotecontrol.data.DataRepository  	getToList ,com.weihai.remotecontrol.data.DataRepository  getToMutableList ,com.weihai.remotecontrol.data.DataRepository  getTrim ,com.weihai.remotecontrol.data.DataRepository  groups ,com.weihai.remotecontrol.data.DataRepository  gson ,com.weihai.remotecontrol.data.DataRepository  indexOfFirst ,com.weihai.remotecontrol.data.DataRepository  initializeDefaultData ,com.weihai.remotecontrol.data.DataRepository  invoke ,com.weihai.remotecontrol.data.DataRepository  isBlank ,com.weihai.remotecontrol.data.DataRepository  
isNotEmpty ,com.weihai.remotecontrol.data.DataRepository  listOf ,com.weihai.remotecontrol.data.DataRepository  loadData ,com.weihai.remotecontrol.data.DataRepository  	lowercase ,com.weihai.remotecontrol.data.DataRepository  
mutableListOf ,com.weihai.remotecontrol.data.DataRepository  	removeAll ,com.weihai.remotecontrol.data.DataRepository  
reorderGroups ,com.weihai.remotecontrol.data.DataRepository  saveData ,com.weihai.remotecontrol.data.DataRepository  
searchDevices ,com.weihai.remotecontrol.data.DataRepository  sharedPreferences ,com.weihai.remotecontrol.data.DataRepository  synchronized ,com.weihai.remotecontrol.data.DataRepository  toList ,com.weihai.remotecontrol.data.DataRepository  
toMutableList ,com.weihai.remotecontrol.data.DataRepository  trim ,com.weihai.remotecontrol.data.DataRepository  updateDevice ,com.weihai.remotecontrol.data.DataRepository  Boolean 6com.weihai.remotecontrol.data.DataRepository.Companion  Context 6com.weihai.remotecontrol.data.DataRepository.Companion  DataRepository 6com.weihai.remotecontrol.data.DataRepository.Companion  Device 6com.weihai.remotecontrol.data.DataRepository.Companion  
DeviceType 6com.weihai.remotecontrol.data.DataRepository.Companion  Gson 6com.weihai.remotecontrol.data.DataRepository.Companion  INSTANCE 6com.weihai.remotecontrol.data.DataRepository.Companion  Int 6com.weihai.remotecontrol.data.DataRepository.Companion  KEY_DEVICES 6com.weihai.remotecontrol.data.DataRepository.Companion  
KEY_GROUPS 6com.weihai.remotecontrol.data.DataRepository.Companion  List 6com.weihai.remotecontrol.data.DataRepository.Companion  
PREFS_NAME 6com.weihai.remotecontrol.data.DataRepository.Companion  SharedPreferences 6com.weihai.remotecontrol.data.DataRepository.Companion  String 6com.weihai.remotecontrol.data.DataRepository.Companion  	TypeToken 6com.weihai.remotecontrol.data.DataRepository.Companion  Volatile 6com.weihai.remotecontrol.data.DataRepository.Companion  _devices 6com.weihai.remotecontrol.data.DataRepository.Companion  _groups 6com.weihai.remotecontrol.data.DataRepository.Companion  also 6com.weihai.remotecontrol.data.DataRepository.Companion  apply 6com.weihai.remotecontrol.data.DataRepository.Companion  contains 6com.weihai.remotecontrol.data.DataRepository.Companion  count 6com.weihai.remotecontrol.data.DataRepository.Companion  filter 6com.weihai.remotecontrol.data.DataRepository.Companion  find 6com.weihai.remotecontrol.data.DataRepository.Companion  getALSO 6com.weihai.remotecontrol.data.DataRepository.Companion  getAPPLY 6com.weihai.remotecontrol.data.DataRepository.Companion  getAlso 6com.weihai.remotecontrol.data.DataRepository.Companion  getApply 6com.weihai.remotecontrol.data.DataRepository.Companion  getCONTAINS 6com.weihai.remotecontrol.data.DataRepository.Companion  getCOUNT 6com.weihai.remotecontrol.data.DataRepository.Companion  getContains 6com.weihai.remotecontrol.data.DataRepository.Companion  getCount 6com.weihai.remotecontrol.data.DataRepository.Companion  	getFILTER 6com.weihai.remotecontrol.data.DataRepository.Companion  getFIND 6com.weihai.remotecontrol.data.DataRepository.Companion  	getFilter 6com.weihai.remotecontrol.data.DataRepository.Companion  getFind 6com.weihai.remotecontrol.data.DataRepository.Companion  getINDEXOfFirst 6com.weihai.remotecontrol.data.DataRepository.Companion  
getISBlank 6com.weihai.remotecontrol.data.DataRepository.Companion  
getISNotEmpty 6com.weihai.remotecontrol.data.DataRepository.Companion  getIndexOfFirst 6com.weihai.remotecontrol.data.DataRepository.Companion  getInstance 6com.weihai.remotecontrol.data.DataRepository.Companion  
getIsBlank 6com.weihai.remotecontrol.data.DataRepository.Companion  
getIsNotEmpty 6com.weihai.remotecontrol.data.DataRepository.Companion  	getLISTOf 6com.weihai.remotecontrol.data.DataRepository.Companion  getLOWERCASE 6com.weihai.remotecontrol.data.DataRepository.Companion  	getListOf 6com.weihai.remotecontrol.data.DataRepository.Companion  getLowercase 6com.weihai.remotecontrol.data.DataRepository.Companion  getMUTABLEListOf 6com.weihai.remotecontrol.data.DataRepository.Companion  getMutableListOf 6com.weihai.remotecontrol.data.DataRepository.Companion  getREMOVEAll 6com.weihai.remotecontrol.data.DataRepository.Companion  getRemoveAll 6com.weihai.remotecontrol.data.DataRepository.Companion  getSYNCHRONIZED 6com.weihai.remotecontrol.data.DataRepository.Companion  getSynchronized 6com.weihai.remotecontrol.data.DataRepository.Companion  	getTOList 6com.weihai.remotecontrol.data.DataRepository.Companion  getTOMutableList 6com.weihai.remotecontrol.data.DataRepository.Companion  getTRIM 6com.weihai.remotecontrol.data.DataRepository.Companion  	getToList 6com.weihai.remotecontrol.data.DataRepository.Companion  getToMutableList 6com.weihai.remotecontrol.data.DataRepository.Companion  getTrim 6com.weihai.remotecontrol.data.DataRepository.Companion  gson 6com.weihai.remotecontrol.data.DataRepository.Companion  indexOfFirst 6com.weihai.remotecontrol.data.DataRepository.Companion  invoke 6com.weihai.remotecontrol.data.DataRepository.Companion  isBlank 6com.weihai.remotecontrol.data.DataRepository.Companion  
isNotEmpty 6com.weihai.remotecontrol.data.DataRepository.Companion  listOf 6com.weihai.remotecontrol.data.DataRepository.Companion  	lowercase 6com.weihai.remotecontrol.data.DataRepository.Companion  
mutableListOf 6com.weihai.remotecontrol.data.DataRepository.Companion  	removeAll 6com.weihai.remotecontrol.data.DataRepository.Companion  synchronized 6com.weihai.remotecontrol.data.DataRepository.Companion  toList 6com.weihai.remotecontrol.data.DataRepository.Companion  
toMutableList 6com.weihai.remotecontrol.data.DataRepository.Companion  trim 6com.weihai.remotecontrol.data.DataRepository.Companion  getTYPE Hcom.weihai.remotecontrol.data.DataRepository.loadData.<no name provided>  getType Hcom.weihai.remotecontrol.data.DataRepository.loadData.<no name provided>  setType Hcom.weihai.remotecontrol.data.DataRepository.loadData.<no name provided>  
DeviceType $com.weihai.remotecontrol.data.Device  SerializedName $com.weihai.remotecontrol.data.Device  String $com.weihai.remotecontrol.data.Device  System $com.weihai.remotecontrol.data.Device  code $com.weihai.remotecontrol.data.Device  copy $com.weihai.remotecontrol.data.Device  equals $com.weihai.remotecontrol.data.Device  
generateId $com.weihai.remotecontrol.data.Device  getLET $com.weihai.remotecontrol.data.Device  getLet $com.weihai.remotecontrol.data.Device  group $com.weihai.remotecontrol.data.Device  id $com.weihai.remotecontrol.data.Device  let $com.weihai.remotecontrol.data.Device  name $com.weihai.remotecontrol.data.Device  password $com.weihai.remotecontrol.data.Device  type $com.weihai.remotecontrol.data.Device  
DeviceType .com.weihai.remotecontrol.data.Device.Companion  SerializedName .com.weihai.remotecontrol.data.Device.Companion  String .com.weihai.remotecontrol.data.Device.Companion  System .com.weihai.remotecontrol.data.Device.Companion  
generateId .com.weihai.remotecontrol.data.Device.Companion  invoke .com.weihai.remotecontrol.data.Device.Companion  
DeviceType (com.weihai.remotecontrol.data.DeviceType  INDOOR (com.weihai.remotecontrol.data.DeviceType  STANDARD (com.weihai.remotecontrol.data.DeviceType  SerializedName (com.weihai.remotecontrol.data.DeviceType  String (com.weihai.remotecontrol.data.DeviceType  displayName (com.weihai.remotecontrol.data.DeviceType  equals (com.weihai.remotecontrol.data.DeviceType  find (com.weihai.remotecontrol.data.DeviceType  value (com.weihai.remotecontrol.data.DeviceType  values (com.weihai.remotecontrol.data.DeviceType  
DeviceType 2com.weihai.remotecontrol.data.DeviceType.Companion  INDOOR 2com.weihai.remotecontrol.data.DeviceType.Companion  STANDARD 2com.weihai.remotecontrol.data.DeviceType.Companion  SerializedName 2com.weihai.remotecontrol.data.DeviceType.Companion  String 2com.weihai.remotecontrol.data.DeviceType.Companion  find 2com.weihai.remotecontrol.data.DeviceType.Companion  getFIND 2com.weihai.remotecontrol.data.DeviceType.Companion  getFind 2com.weihai.remotecontrol.data.DeviceType.Companion  	getVALUES 2com.weihai.remotecontrol.data.DeviceType.Companion  	getValues 2com.weihai.remotecontrol.data.DeviceType.Companion  values 2com.weihai.remotecontrol.data.DeviceType.Companion  ActivityMainBinding $com.weihai.remotecontrol.databinding  DialogAddDeviceBinding $com.weihai.remotecontrol.databinding  FragmentDevicesBinding $com.weihai.remotecontrol.databinding  FragmentStatisticsBinding $com.weihai.remotecontrol.databinding  ItemDeviceBinding $com.weihai.remotecontrol.databinding  ItemGroupStatisticBinding $com.weihai.remotecontrol.databinding  bottomNavigation 8com.weihai.remotecontrol.databinding.ActivityMainBinding  btnAddDevice 8com.weihai.remotecontrol.databinding.ActivityMainBinding  btnAddGroup 8com.weihai.remotecontrol.databinding.ActivityMainBinding  etSearch 8com.weihai.remotecontrol.databinding.ActivityMainBinding  getROOT 8com.weihai.remotecontrol.databinding.ActivityMainBinding  getRoot 8com.weihai.remotecontrol.databinding.ActivityMainBinding  inflate 8com.weihai.remotecontrol.databinding.ActivityMainBinding  root 8com.weihai.remotecontrol.databinding.ActivityMainBinding  setRoot 8com.weihai.remotecontrol.databinding.ActivityMainBinding  	viewPager 8com.weihai.remotecontrol.databinding.ActivityMainBinding  actvDeviceGroup ;com.weihai.remotecontrol.databinding.DialogAddDeviceBinding  actvDeviceType ;com.weihai.remotecontrol.databinding.DialogAddDeviceBinding  	btnCancel ;com.weihai.remotecontrol.databinding.DialogAddDeviceBinding  btnSave ;com.weihai.remotecontrol.databinding.DialogAddDeviceBinding  etDeviceCode ;com.weihai.remotecontrol.databinding.DialogAddDeviceBinding  etDeviceName ;com.weihai.remotecontrol.databinding.DialogAddDeviceBinding  etDevicePassword ;com.weihai.remotecontrol.databinding.DialogAddDeviceBinding  getROOT ;com.weihai.remotecontrol.databinding.DialogAddDeviceBinding  getRoot ;com.weihai.remotecontrol.databinding.DialogAddDeviceBinding  inflate ;com.weihai.remotecontrol.databinding.DialogAddDeviceBinding  root ;com.weihai.remotecontrol.databinding.DialogAddDeviceBinding  setRoot ;com.weihai.remotecontrol.databinding.DialogAddDeviceBinding  
tilDeviceCode ;com.weihai.remotecontrol.databinding.DialogAddDeviceBinding  
tilDeviceName ;com.weihai.remotecontrol.databinding.DialogAddDeviceBinding  tilDevicePassword ;com.weihai.remotecontrol.databinding.DialogAddDeviceBinding  
chipGroupTabs ;com.weihai.remotecontrol.databinding.FragmentDevicesBinding  getROOT ;com.weihai.remotecontrol.databinding.FragmentDevicesBinding  getRoot ;com.weihai.remotecontrol.databinding.FragmentDevicesBinding  inflate ;com.weihai.remotecontrol.databinding.FragmentDevicesBinding  layoutEmpty ;com.weihai.remotecontrol.databinding.FragmentDevicesBinding  recyclerViewDevices ;com.weihai.remotecontrol.databinding.FragmentDevicesBinding  root ;com.weihai.remotecontrol.databinding.FragmentDevicesBinding  setRoot ;com.weihai.remotecontrol.databinding.FragmentDevicesBinding  tvEmptyMessage ;com.weihai.remotecontrol.databinding.FragmentDevicesBinding  
tvEmptyTip ;com.weihai.remotecontrol.databinding.FragmentDevicesBinding  getROOT >com.weihai.remotecontrol.databinding.FragmentStatisticsBinding  getRoot >com.weihai.remotecontrol.databinding.FragmentStatisticsBinding  inflate >com.weihai.remotecontrol.databinding.FragmentStatisticsBinding  recyclerViewGroupStats >com.weihai.remotecontrol.databinding.FragmentStatisticsBinding  root >com.weihai.remotecontrol.databinding.FragmentStatisticsBinding  setRoot >com.weihai.remotecontrol.databinding.FragmentStatisticsBinding  tvIndoorDevices >com.weihai.remotecontrol.databinding.FragmentStatisticsBinding  tvStandardDevices >com.weihai.remotecontrol.databinding.FragmentStatisticsBinding  tvTotalDevices >com.weihai.remotecontrol.databinding.FragmentStatisticsBinding  
tvTotalGroups >com.weihai.remotecontrol.databinding.FragmentStatisticsBinding  
ContextCompat 6com.weihai.remotecontrol.databinding.ItemDeviceBinding  
DeviceType 6com.weihai.remotecontrol.databinding.ItemDeviceBinding  R 6com.weihai.remotecontrol.databinding.ItemDeviceBinding  apply 6com.weihai.remotecontrol.databinding.ItemDeviceBinding  btnCopyCode 6com.weihai.remotecontrol.databinding.ItemDeviceBinding  btnCopyPassword 6com.weihai.remotecontrol.databinding.ItemDeviceBinding  btnDeleteDevice 6com.weihai.remotecontrol.databinding.ItemDeviceBinding  
btnEditDevice 6com.weihai.remotecontrol.databinding.ItemDeviceBinding  	chipGroup 6com.weihai.remotecontrol.databinding.ItemDeviceBinding  chipType 6com.weihai.remotecontrol.databinding.ItemDeviceBinding  copyToClipboard 6com.weihai.remotecontrol.databinding.ItemDeviceBinding  getAPPLY 6com.weihai.remotecontrol.databinding.ItemDeviceBinding  getApply 6com.weihai.remotecontrol.databinding.ItemDeviceBinding  getCOPYToClipboard 6com.weihai.remotecontrol.databinding.ItemDeviceBinding  getCopyToClipboard 6com.weihai.remotecontrol.databinding.ItemDeviceBinding  getONDeleteClick 6com.weihai.remotecontrol.databinding.ItemDeviceBinding  getONEditClick 6com.weihai.remotecontrol.databinding.ItemDeviceBinding  getOnDeleteClick 6com.weihai.remotecontrol.databinding.ItemDeviceBinding  getOnEditClick 6com.weihai.remotecontrol.databinding.ItemDeviceBinding  getROOT 6com.weihai.remotecontrol.databinding.ItemDeviceBinding  getRoot 6com.weihai.remotecontrol.databinding.ItemDeviceBinding  inflate 6com.weihai.remotecontrol.databinding.ItemDeviceBinding  invoke 6com.weihai.remotecontrol.databinding.ItemDeviceBinding  
onDeleteClick 6com.weihai.remotecontrol.databinding.ItemDeviceBinding  onEditClick 6com.weihai.remotecontrol.databinding.ItemDeviceBinding  root 6com.weihai.remotecontrol.databinding.ItemDeviceBinding  setRoot 6com.weihai.remotecontrol.databinding.ItemDeviceBinding  tvDeviceCode 6com.weihai.remotecontrol.databinding.ItemDeviceBinding  tvDeviceName 6com.weihai.remotecontrol.databinding.ItemDeviceBinding  tvDevicePassword 6com.weihai.remotecontrol.databinding.ItemDeviceBinding  R >com.weihai.remotecontrol.databinding.ItemGroupStatisticBinding  apply >com.weihai.remotecontrol.databinding.ItemGroupStatisticBinding  getAPPLY >com.weihai.remotecontrol.databinding.ItemGroupStatisticBinding  getApply >com.weihai.remotecontrol.databinding.ItemGroupStatisticBinding  getROOT >com.weihai.remotecontrol.databinding.ItemGroupStatisticBinding  getRoot >com.weihai.remotecontrol.databinding.ItemGroupStatisticBinding  inflate >com.weihai.remotecontrol.databinding.ItemGroupStatisticBinding  root >com.weihai.remotecontrol.databinding.ItemGroupStatisticBinding  setRoot >com.weihai.remotecontrol.databinding.ItemGroupStatisticBinding  
tvDeviceCount >com.weihai.remotecontrol.databinding.ItemGroupStatisticBinding  tvGroupName >com.weihai.remotecontrol.databinding.ItemGroupStatisticBinding  
tvIndoorCount >com.weihai.remotecontrol.databinding.ItemGroupStatisticBinding  tvStandardCount >com.weihai.remotecontrol.databinding.ItemGroupStatisticBinding  Boolean #com.weihai.remotecontrol.ui.adapter  ClipData #com.weihai.remotecontrol.ui.adapter  Context #com.weihai.remotecontrol.ui.adapter  
ContextCompat #com.weihai.remotecontrol.ui.adapter  
DeviceAdapter #com.weihai.remotecontrol.ui.adapter  DeviceDiffCallback #com.weihai.remotecontrol.ui.adapter  
DeviceType #com.weihai.remotecontrol.ui.adapter  GroupStatisticAdapter #com.weihai.remotecontrol.ui.adapter  GroupStatisticDiffCallback #com.weihai.remotecontrol.ui.adapter  Int #com.weihai.remotecontrol.ui.adapter  ItemDeviceBinding #com.weihai.remotecontrol.ui.adapter  ItemGroupStatisticBinding #com.weihai.remotecontrol.ui.adapter  LayoutInflater #com.weihai.remotecontrol.ui.adapter  R #com.weihai.remotecontrol.ui.adapter  String #com.weihai.remotecontrol.ui.adapter  Toast #com.weihai.remotecontrol.ui.adapter  Unit #com.weihai.remotecontrol.ui.adapter  
ViewHolder #com.weihai.remotecontrol.ui.adapter  apply #com.weihai.remotecontrol.ui.adapter  copyToClipboard #com.weihai.remotecontrol.ui.adapter  
onDeleteClick #com.weihai.remotecontrol.ui.adapter  onEditClick #com.weihai.remotecontrol.ui.adapter  ClipData 1com.weihai.remotecontrol.ui.adapter.DeviceAdapter  ClipboardManager 1com.weihai.remotecontrol.ui.adapter.DeviceAdapter  Context 1com.weihai.remotecontrol.ui.adapter.DeviceAdapter  
ContextCompat 1com.weihai.remotecontrol.ui.adapter.DeviceAdapter  Device 1com.weihai.remotecontrol.ui.adapter.DeviceAdapter  DeviceDiffCallback 1com.weihai.remotecontrol.ui.adapter.DeviceAdapter  
DeviceType 1com.weihai.remotecontrol.ui.adapter.DeviceAdapter  DeviceViewHolder 1com.weihai.remotecontrol.ui.adapter.DeviceAdapter  Int 1com.weihai.remotecontrol.ui.adapter.DeviceAdapter  ItemDeviceBinding 1com.weihai.remotecontrol.ui.adapter.DeviceAdapter  LayoutInflater 1com.weihai.remotecontrol.ui.adapter.DeviceAdapter  R 1com.weihai.remotecontrol.ui.adapter.DeviceAdapter  RecyclerView 1com.weihai.remotecontrol.ui.adapter.DeviceAdapter  String 1com.weihai.remotecontrol.ui.adapter.DeviceAdapter  Toast 1com.weihai.remotecontrol.ui.adapter.DeviceAdapter  Unit 1com.weihai.remotecontrol.ui.adapter.DeviceAdapter  	ViewGroup 1com.weihai.remotecontrol.ui.adapter.DeviceAdapter  apply 1com.weihai.remotecontrol.ui.adapter.DeviceAdapter  copyToClipboard 1com.weihai.remotecontrol.ui.adapter.DeviceAdapter  getAPPLY 1com.weihai.remotecontrol.ui.adapter.DeviceAdapter  getApply 1com.weihai.remotecontrol.ui.adapter.DeviceAdapter  getItem 1com.weihai.remotecontrol.ui.adapter.DeviceAdapter  invoke 1com.weihai.remotecontrol.ui.adapter.DeviceAdapter  
onDeleteClick 1com.weihai.remotecontrol.ui.adapter.DeviceAdapter  onEditClick 1com.weihai.remotecontrol.ui.adapter.DeviceAdapter  
submitList 1com.weihai.remotecontrol.ui.adapter.DeviceAdapter  ClipData Bcom.weihai.remotecontrol.ui.adapter.DeviceAdapter.DeviceViewHolder  ClipboardManager Bcom.weihai.remotecontrol.ui.adapter.DeviceAdapter.DeviceViewHolder  Context Bcom.weihai.remotecontrol.ui.adapter.DeviceAdapter.DeviceViewHolder  
ContextCompat Bcom.weihai.remotecontrol.ui.adapter.DeviceAdapter.DeviceViewHolder  Device Bcom.weihai.remotecontrol.ui.adapter.DeviceAdapter.DeviceViewHolder  
DeviceType Bcom.weihai.remotecontrol.ui.adapter.DeviceAdapter.DeviceViewHolder  ItemDeviceBinding Bcom.weihai.remotecontrol.ui.adapter.DeviceAdapter.DeviceViewHolder  R Bcom.weihai.remotecontrol.ui.adapter.DeviceAdapter.DeviceViewHolder  String Bcom.weihai.remotecontrol.ui.adapter.DeviceAdapter.DeviceViewHolder  Toast Bcom.weihai.remotecontrol.ui.adapter.DeviceAdapter.DeviceViewHolder  apply Bcom.weihai.remotecontrol.ui.adapter.DeviceAdapter.DeviceViewHolder  bind Bcom.weihai.remotecontrol.ui.adapter.DeviceAdapter.DeviceViewHolder  binding Bcom.weihai.remotecontrol.ui.adapter.DeviceAdapter.DeviceViewHolder  copyToClipboard Bcom.weihai.remotecontrol.ui.adapter.DeviceAdapter.DeviceViewHolder  getAPPLY Bcom.weihai.remotecontrol.ui.adapter.DeviceAdapter.DeviceViewHolder  getApply Bcom.weihai.remotecontrol.ui.adapter.DeviceAdapter.DeviceViewHolder  getONDeleteClick Bcom.weihai.remotecontrol.ui.adapter.DeviceAdapter.DeviceViewHolder  getONEditClick Bcom.weihai.remotecontrol.ui.adapter.DeviceAdapter.DeviceViewHolder  getOnDeleteClick Bcom.weihai.remotecontrol.ui.adapter.DeviceAdapter.DeviceViewHolder  getOnEditClick Bcom.weihai.remotecontrol.ui.adapter.DeviceAdapter.DeviceViewHolder  invoke Bcom.weihai.remotecontrol.ui.adapter.DeviceAdapter.DeviceViewHolder  
onDeleteClick Bcom.weihai.remotecontrol.ui.adapter.DeviceAdapter.DeviceViewHolder  onEditClick Bcom.weihai.remotecontrol.ui.adapter.DeviceAdapter.DeviceViewHolder  Boolean 6com.weihai.remotecontrol.ui.adapter.DeviceDiffCallback  Device 6com.weihai.remotecontrol.ui.adapter.DeviceDiffCallback  GroupStatistic 9com.weihai.remotecontrol.ui.adapter.GroupStatisticAdapter  GroupStatisticDiffCallback 9com.weihai.remotecontrol.ui.adapter.GroupStatisticAdapter  Int 9com.weihai.remotecontrol.ui.adapter.GroupStatisticAdapter  ItemGroupStatisticBinding 9com.weihai.remotecontrol.ui.adapter.GroupStatisticAdapter  LayoutInflater 9com.weihai.remotecontrol.ui.adapter.GroupStatisticAdapter  R 9com.weihai.remotecontrol.ui.adapter.GroupStatisticAdapter  RecyclerView 9com.weihai.remotecontrol.ui.adapter.GroupStatisticAdapter  	ViewGroup 9com.weihai.remotecontrol.ui.adapter.GroupStatisticAdapter  
ViewHolder 9com.weihai.remotecontrol.ui.adapter.GroupStatisticAdapter  apply 9com.weihai.remotecontrol.ui.adapter.GroupStatisticAdapter  getItem 9com.weihai.remotecontrol.ui.adapter.GroupStatisticAdapter  
submitList 9com.weihai.remotecontrol.ui.adapter.GroupStatisticAdapter  GroupStatistic Dcom.weihai.remotecontrol.ui.adapter.GroupStatisticAdapter.ViewHolder  ItemGroupStatisticBinding Dcom.weihai.remotecontrol.ui.adapter.GroupStatisticAdapter.ViewHolder  R Dcom.weihai.remotecontrol.ui.adapter.GroupStatisticAdapter.ViewHolder  apply Dcom.weihai.remotecontrol.ui.adapter.GroupStatisticAdapter.ViewHolder  bind Dcom.weihai.remotecontrol.ui.adapter.GroupStatisticAdapter.ViewHolder  binding Dcom.weihai.remotecontrol.ui.adapter.GroupStatisticAdapter.ViewHolder  getAPPLY Dcom.weihai.remotecontrol.ui.adapter.GroupStatisticAdapter.ViewHolder  getApply Dcom.weihai.remotecontrol.ui.adapter.GroupStatisticAdapter.ViewHolder  Boolean >com.weihai.remotecontrol.ui.adapter.GroupStatisticDiffCallback  GroupStatistic >com.weihai.remotecontrol.ui.adapter.GroupStatisticDiffCallback  AddDeviceDialog "com.weihai.remotecontrol.ui.dialog  AddGroupDialog "com.weihai.remotecontrol.ui.dialog  AlertDialog "com.weihai.remotecontrol.ui.dialog  ArrayAdapter "com.weihai.remotecontrol.ui.dialog  Boolean "com.weihai.remotecontrol.ui.dialog  DataRepository "com.weihai.remotecontrol.ui.dialog  DeleteConfirmDialog "com.weihai.remotecontrol.ui.dialog  Device "com.weihai.remotecontrol.ui.dialog  
DeviceType "com.weihai.remotecontrol.ui.dialog  DialogAddDeviceBinding "com.weihai.remotecontrol.ui.dialog  EditDeviceDialog "com.weihai.remotecontrol.ui.dialog  EditGroupDialog "com.weihai.remotecontrol.ui.dialog  EditText "com.weihai.remotecontrol.ui.dialog  R "com.weihai.remotecontrol.ui.dialog  String "com.weihai.remotecontrol.ui.dialog  Unit "com.weihai.remotecontrol.ui.dialog  android "com.weihai.remotecontrol.ui.dialog  apply "com.weihai.remotecontrol.ui.dialog  	getString "com.weihai.remotecontrol.ui.dialog  invoke "com.weihai.remotecontrol.ui.dialog  
isNotBlank "com.weihai.remotecontrol.ui.dialog  
isNotEmpty "com.weihai.remotecontrol.ui.dialog  
isNullOrBlank "com.weihai.remotecontrol.ui.dialog  let "com.weihai.remotecontrol.ui.dialog  listOf "com.weihai.remotecontrol.ui.dialog  originalName "com.weihai.remotecontrol.ui.dialog  toString "com.weihai.remotecontrol.ui.dialog  trim "com.weihai.remotecontrol.ui.dialog  AddDeviceDialog 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  AlertDialog 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  ArrayAdapter 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  Boolean 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  Bundle 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  DataRepository 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  Device 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  
DeviceType 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  Dialog 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  DialogAddDeviceBinding 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  FragmentManager 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  R 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  Unit 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  _binding 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  android 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  binding 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  createDevice 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  dismiss 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  
getANDROID 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  
getAndroid 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  
getISNotEmpty 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  getISNullOrBlank 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  
getIsNotEmpty 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  getIsNullOrBlank 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  getLAYOUTInflater 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  	getLISTOf 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  getLayoutInflater 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  	getListOf 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  	getString 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  getTOString 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  getTRIM 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  getToString 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  getTrim 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  invoke 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  
isNotEmpty 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  
isNullOrBlank 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  layoutInflater 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  listOf 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  onSaveCallback 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  requireContext 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  setLayoutInflater 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  setupClickListeners 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  setupDropdowns 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  show 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  toString 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  trim 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  
validateInput 2com.weihai.remotecontrol.ui.dialog.AddDeviceDialog  AddDeviceDialog <com.weihai.remotecontrol.ui.dialog.AddDeviceDialog.Companion  AlertDialog <com.weihai.remotecontrol.ui.dialog.AddDeviceDialog.Companion  ArrayAdapter <com.weihai.remotecontrol.ui.dialog.AddDeviceDialog.Companion  Boolean <com.weihai.remotecontrol.ui.dialog.AddDeviceDialog.Companion  Bundle <com.weihai.remotecontrol.ui.dialog.AddDeviceDialog.Companion  DataRepository <com.weihai.remotecontrol.ui.dialog.AddDeviceDialog.Companion  Device <com.weihai.remotecontrol.ui.dialog.AddDeviceDialog.Companion  
DeviceType <com.weihai.remotecontrol.ui.dialog.AddDeviceDialog.Companion  Dialog <com.weihai.remotecontrol.ui.dialog.AddDeviceDialog.Companion  DialogAddDeviceBinding <com.weihai.remotecontrol.ui.dialog.AddDeviceDialog.Companion  FragmentManager <com.weihai.remotecontrol.ui.dialog.AddDeviceDialog.Companion  R <com.weihai.remotecontrol.ui.dialog.AddDeviceDialog.Companion  Unit <com.weihai.remotecontrol.ui.dialog.AddDeviceDialog.Companion  android <com.weihai.remotecontrol.ui.dialog.AddDeviceDialog.Companion  
getANDROID <com.weihai.remotecontrol.ui.dialog.AddDeviceDialog.Companion  
getAndroid <com.weihai.remotecontrol.ui.dialog.AddDeviceDialog.Companion  
getISNotEmpty <com.weihai.remotecontrol.ui.dialog.AddDeviceDialog.Companion  getISNullOrBlank <com.weihai.remotecontrol.ui.dialog.AddDeviceDialog.Companion  
getIsNotEmpty <com.weihai.remotecontrol.ui.dialog.AddDeviceDialog.Companion  getIsNullOrBlank <com.weihai.remotecontrol.ui.dialog.AddDeviceDialog.Companion  	getLISTOf <com.weihai.remotecontrol.ui.dialog.AddDeviceDialog.Companion  	getListOf <com.weihai.remotecontrol.ui.dialog.AddDeviceDialog.Companion  getTOString <com.weihai.remotecontrol.ui.dialog.AddDeviceDialog.Companion  getTRIM <com.weihai.remotecontrol.ui.dialog.AddDeviceDialog.Companion  getToString <com.weihai.remotecontrol.ui.dialog.AddDeviceDialog.Companion  getTrim <com.weihai.remotecontrol.ui.dialog.AddDeviceDialog.Companion  invoke <com.weihai.remotecontrol.ui.dialog.AddDeviceDialog.Companion  
isNotEmpty <com.weihai.remotecontrol.ui.dialog.AddDeviceDialog.Companion  
isNullOrBlank <com.weihai.remotecontrol.ui.dialog.AddDeviceDialog.Companion  listOf <com.weihai.remotecontrol.ui.dialog.AddDeviceDialog.Companion  show <com.weihai.remotecontrol.ui.dialog.AddDeviceDialog.Companion  toString <com.weihai.remotecontrol.ui.dialog.AddDeviceDialog.Companion  trim <com.weihai.remotecontrol.ui.dialog.AddDeviceDialog.Companion  AddGroupDialog 1com.weihai.remotecontrol.ui.dialog.AddGroupDialog  AlertDialog 1com.weihai.remotecontrol.ui.dialog.AddGroupDialog  Bundle 1com.weihai.remotecontrol.ui.dialog.AddGroupDialog  Dialog 1com.weihai.remotecontrol.ui.dialog.AddGroupDialog  EditText 1com.weihai.remotecontrol.ui.dialog.AddGroupDialog  FragmentManager 1com.weihai.remotecontrol.ui.dialog.AddGroupDialog  R 1com.weihai.remotecontrol.ui.dialog.AddGroupDialog  String 1com.weihai.remotecontrol.ui.dialog.AddGroupDialog  Unit 1com.weihai.remotecontrol.ui.dialog.AddGroupDialog  apply 1com.weihai.remotecontrol.ui.dialog.AddGroupDialog  getAPPLY 1com.weihai.remotecontrol.ui.dialog.AddGroupDialog  getApply 1com.weihai.remotecontrol.ui.dialog.AddGroupDialog  
getISNotBlank 1com.weihai.remotecontrol.ui.dialog.AddGroupDialog  
getIsNotBlank 1com.weihai.remotecontrol.ui.dialog.AddGroupDialog  	getString 1com.weihai.remotecontrol.ui.dialog.AddGroupDialog  getTRIM 1com.weihai.remotecontrol.ui.dialog.AddGroupDialog  getTrim 1com.weihai.remotecontrol.ui.dialog.AddGroupDialog  invoke 1com.weihai.remotecontrol.ui.dialog.AddGroupDialog  
isNotBlank 1com.weihai.remotecontrol.ui.dialog.AddGroupDialog  onSaveCallback 1com.weihai.remotecontrol.ui.dialog.AddGroupDialog  requireContext 1com.weihai.remotecontrol.ui.dialog.AddGroupDialog  show 1com.weihai.remotecontrol.ui.dialog.AddGroupDialog  trim 1com.weihai.remotecontrol.ui.dialog.AddGroupDialog  AddGroupDialog ;com.weihai.remotecontrol.ui.dialog.AddGroupDialog.Companion  AlertDialog ;com.weihai.remotecontrol.ui.dialog.AddGroupDialog.Companion  Bundle ;com.weihai.remotecontrol.ui.dialog.AddGroupDialog.Companion  Dialog ;com.weihai.remotecontrol.ui.dialog.AddGroupDialog.Companion  EditText ;com.weihai.remotecontrol.ui.dialog.AddGroupDialog.Companion  FragmentManager ;com.weihai.remotecontrol.ui.dialog.AddGroupDialog.Companion  R ;com.weihai.remotecontrol.ui.dialog.AddGroupDialog.Companion  String ;com.weihai.remotecontrol.ui.dialog.AddGroupDialog.Companion  Unit ;com.weihai.remotecontrol.ui.dialog.AddGroupDialog.Companion  apply ;com.weihai.remotecontrol.ui.dialog.AddGroupDialog.Companion  getAPPLY ;com.weihai.remotecontrol.ui.dialog.AddGroupDialog.Companion  getApply ;com.weihai.remotecontrol.ui.dialog.AddGroupDialog.Companion  
getISNotBlank ;com.weihai.remotecontrol.ui.dialog.AddGroupDialog.Companion  
getIsNotBlank ;com.weihai.remotecontrol.ui.dialog.AddGroupDialog.Companion  	getString ;com.weihai.remotecontrol.ui.dialog.AddGroupDialog.Companion  getTRIM ;com.weihai.remotecontrol.ui.dialog.AddGroupDialog.Companion  getTrim ;com.weihai.remotecontrol.ui.dialog.AddGroupDialog.Companion  invoke ;com.weihai.remotecontrol.ui.dialog.AddGroupDialog.Companion  
isNotBlank ;com.weihai.remotecontrol.ui.dialog.AddGroupDialog.Companion  show ;com.weihai.remotecontrol.ui.dialog.AddGroupDialog.Companion  trim ;com.weihai.remotecontrol.ui.dialog.AddGroupDialog.Companion  AlertDialog 6com.weihai.remotecontrol.ui.dialog.DeleteConfirmDialog  Bundle 6com.weihai.remotecontrol.ui.dialog.DeleteConfirmDialog  DeleteConfirmDialog 6com.weihai.remotecontrol.ui.dialog.DeleteConfirmDialog  Dialog 6com.weihai.remotecontrol.ui.dialog.DeleteConfirmDialog  FragmentManager 6com.weihai.remotecontrol.ui.dialog.DeleteConfirmDialog  R 6com.weihai.remotecontrol.ui.dialog.DeleteConfirmDialog  String 6com.weihai.remotecontrol.ui.dialog.DeleteConfirmDialog  Unit 6com.weihai.remotecontrol.ui.dialog.DeleteConfirmDialog  invoke 6com.weihai.remotecontrol.ui.dialog.DeleteConfirmDialog  message 6com.weihai.remotecontrol.ui.dialog.DeleteConfirmDialog  onConfirmCallback 6com.weihai.remotecontrol.ui.dialog.DeleteConfirmDialog  requireContext 6com.weihai.remotecontrol.ui.dialog.DeleteConfirmDialog  show 6com.weihai.remotecontrol.ui.dialog.DeleteConfirmDialog  title 6com.weihai.remotecontrol.ui.dialog.DeleteConfirmDialog  AlertDialog @com.weihai.remotecontrol.ui.dialog.DeleteConfirmDialog.Companion  Bundle @com.weihai.remotecontrol.ui.dialog.DeleteConfirmDialog.Companion  DeleteConfirmDialog @com.weihai.remotecontrol.ui.dialog.DeleteConfirmDialog.Companion  Dialog @com.weihai.remotecontrol.ui.dialog.DeleteConfirmDialog.Companion  FragmentManager @com.weihai.remotecontrol.ui.dialog.DeleteConfirmDialog.Companion  R @com.weihai.remotecontrol.ui.dialog.DeleteConfirmDialog.Companion  String @com.weihai.remotecontrol.ui.dialog.DeleteConfirmDialog.Companion  Unit @com.weihai.remotecontrol.ui.dialog.DeleteConfirmDialog.Companion  invoke @com.weihai.remotecontrol.ui.dialog.DeleteConfirmDialog.Companion  show @com.weihai.remotecontrol.ui.dialog.DeleteConfirmDialog.Companion  AlertDialog 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  ArrayAdapter 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  Boolean 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  Bundle 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  DataRepository 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  Device 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  
DeviceType 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  Dialog 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  DialogAddDeviceBinding 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  EditDeviceDialog 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  FragmentManager 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  R 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  Unit 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  _binding 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  android 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  binding 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  createDevice 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  device 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  dismiss 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  
getANDROID 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  
getAndroid 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  getISNullOrBlank 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  getIsNullOrBlank 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  getLAYOUTInflater 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  getLET 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  	getLISTOf 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  getLayoutInflater 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  getLet 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  	getListOf 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  	getString 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  getTOString 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  getTRIM 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  getToString 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  getTrim 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  invoke 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  
isNullOrBlank 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  layoutInflater 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  let 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  listOf 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  onSaveCallback 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  populateFields 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  requireContext 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  setLayoutInflater 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  setupClickListeners 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  setupDropdowns 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  show 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  toString 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  trim 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  
validateInput 3com.weihai.remotecontrol.ui.dialog.EditDeviceDialog  AlertDialog =com.weihai.remotecontrol.ui.dialog.EditDeviceDialog.Companion  ArrayAdapter =com.weihai.remotecontrol.ui.dialog.EditDeviceDialog.Companion  Boolean =com.weihai.remotecontrol.ui.dialog.EditDeviceDialog.Companion  Bundle =com.weihai.remotecontrol.ui.dialog.EditDeviceDialog.Companion  DataRepository =com.weihai.remotecontrol.ui.dialog.EditDeviceDialog.Companion  Device =com.weihai.remotecontrol.ui.dialog.EditDeviceDialog.Companion  
DeviceType =com.weihai.remotecontrol.ui.dialog.EditDeviceDialog.Companion  Dialog =com.weihai.remotecontrol.ui.dialog.EditDeviceDialog.Companion  DialogAddDeviceBinding =com.weihai.remotecontrol.ui.dialog.EditDeviceDialog.Companion  EditDeviceDialog =com.weihai.remotecontrol.ui.dialog.EditDeviceDialog.Companion  FragmentManager =com.weihai.remotecontrol.ui.dialog.EditDeviceDialog.Companion  R =com.weihai.remotecontrol.ui.dialog.EditDeviceDialog.Companion  Unit =com.weihai.remotecontrol.ui.dialog.EditDeviceDialog.Companion  android =com.weihai.remotecontrol.ui.dialog.EditDeviceDialog.Companion  
getANDROID =com.weihai.remotecontrol.ui.dialog.EditDeviceDialog.Companion  
getAndroid =com.weihai.remotecontrol.ui.dialog.EditDeviceDialog.Companion  getISNullOrBlank =com.weihai.remotecontrol.ui.dialog.EditDeviceDialog.Companion  getIsNullOrBlank =com.weihai.remotecontrol.ui.dialog.EditDeviceDialog.Companion  getLET =com.weihai.remotecontrol.ui.dialog.EditDeviceDialog.Companion  	getLISTOf =com.weihai.remotecontrol.ui.dialog.EditDeviceDialog.Companion  getLet =com.weihai.remotecontrol.ui.dialog.EditDeviceDialog.Companion  	getListOf =com.weihai.remotecontrol.ui.dialog.EditDeviceDialog.Companion  getTOString =com.weihai.remotecontrol.ui.dialog.EditDeviceDialog.Companion  getTRIM =com.weihai.remotecontrol.ui.dialog.EditDeviceDialog.Companion  getToString =com.weihai.remotecontrol.ui.dialog.EditDeviceDialog.Companion  getTrim =com.weihai.remotecontrol.ui.dialog.EditDeviceDialog.Companion  invoke =com.weihai.remotecontrol.ui.dialog.EditDeviceDialog.Companion  
isNullOrBlank =com.weihai.remotecontrol.ui.dialog.EditDeviceDialog.Companion  let =com.weihai.remotecontrol.ui.dialog.EditDeviceDialog.Companion  listOf =com.weihai.remotecontrol.ui.dialog.EditDeviceDialog.Companion  show =com.weihai.remotecontrol.ui.dialog.EditDeviceDialog.Companion  toString =com.weihai.remotecontrol.ui.dialog.EditDeviceDialog.Companion  trim =com.weihai.remotecontrol.ui.dialog.EditDeviceDialog.Companion  AlertDialog 2com.weihai.remotecontrol.ui.dialog.EditGroupDialog  Bundle 2com.weihai.remotecontrol.ui.dialog.EditGroupDialog  Dialog 2com.weihai.remotecontrol.ui.dialog.EditGroupDialog  EditGroupDialog 2com.weihai.remotecontrol.ui.dialog.EditGroupDialog  EditText 2com.weihai.remotecontrol.ui.dialog.EditGroupDialog  FragmentManager 2com.weihai.remotecontrol.ui.dialog.EditGroupDialog  R 2com.weihai.remotecontrol.ui.dialog.EditGroupDialog  String 2com.weihai.remotecontrol.ui.dialog.EditGroupDialog  Unit 2com.weihai.remotecontrol.ui.dialog.EditGroupDialog  apply 2com.weihai.remotecontrol.ui.dialog.EditGroupDialog  getAPPLY 2com.weihai.remotecontrol.ui.dialog.EditGroupDialog  getApply 2com.weihai.remotecontrol.ui.dialog.EditGroupDialog  
getISNotBlank 2com.weihai.remotecontrol.ui.dialog.EditGroupDialog  
getIsNotBlank 2com.weihai.remotecontrol.ui.dialog.EditGroupDialog  	getString 2com.weihai.remotecontrol.ui.dialog.EditGroupDialog  getTRIM 2com.weihai.remotecontrol.ui.dialog.EditGroupDialog  getTrim 2com.weihai.remotecontrol.ui.dialog.EditGroupDialog  invoke 2com.weihai.remotecontrol.ui.dialog.EditGroupDialog  
isNotBlank 2com.weihai.remotecontrol.ui.dialog.EditGroupDialog  onSaveCallback 2com.weihai.remotecontrol.ui.dialog.EditGroupDialog  originalName 2com.weihai.remotecontrol.ui.dialog.EditGroupDialog  requireContext 2com.weihai.remotecontrol.ui.dialog.EditGroupDialog  show 2com.weihai.remotecontrol.ui.dialog.EditGroupDialog  trim 2com.weihai.remotecontrol.ui.dialog.EditGroupDialog  AlertDialog <com.weihai.remotecontrol.ui.dialog.EditGroupDialog.Companion  Bundle <com.weihai.remotecontrol.ui.dialog.EditGroupDialog.Companion  Dialog <com.weihai.remotecontrol.ui.dialog.EditGroupDialog.Companion  EditGroupDialog <com.weihai.remotecontrol.ui.dialog.EditGroupDialog.Companion  EditText <com.weihai.remotecontrol.ui.dialog.EditGroupDialog.Companion  FragmentManager <com.weihai.remotecontrol.ui.dialog.EditGroupDialog.Companion  R <com.weihai.remotecontrol.ui.dialog.EditGroupDialog.Companion  String <com.weihai.remotecontrol.ui.dialog.EditGroupDialog.Companion  Unit <com.weihai.remotecontrol.ui.dialog.EditGroupDialog.Companion  apply <com.weihai.remotecontrol.ui.dialog.EditGroupDialog.Companion  getAPPLY <com.weihai.remotecontrol.ui.dialog.EditGroupDialog.Companion  getApply <com.weihai.remotecontrol.ui.dialog.EditGroupDialog.Companion  
getISNotBlank <com.weihai.remotecontrol.ui.dialog.EditGroupDialog.Companion  
getIsNotBlank <com.weihai.remotecontrol.ui.dialog.EditGroupDialog.Companion  	getString <com.weihai.remotecontrol.ui.dialog.EditGroupDialog.Companion  getTRIM <com.weihai.remotecontrol.ui.dialog.EditGroupDialog.Companion  getTrim <com.weihai.remotecontrol.ui.dialog.EditGroupDialog.Companion  invoke <com.weihai.remotecontrol.ui.dialog.EditGroupDialog.Companion  
isNotBlank <com.weihai.remotecontrol.ui.dialog.EditGroupDialog.Companion  originalName <com.weihai.remotecontrol.ui.dialog.EditGroupDialog.Companion  show <com.weihai.remotecontrol.ui.dialog.EditGroupDialog.Companion  trim <com.weihai.remotecontrol.ui.dialog.EditGroupDialog.Companion  Boolean $com.weihai.remotecontrol.ui.fragment  Chip $com.weihai.remotecontrol.ui.fragment  
DeviceAdapter $com.weihai.remotecontrol.ui.fragment  DevicesFragment $com.weihai.remotecontrol.ui.fragment  FragmentDevicesBinding $com.weihai.remotecontrol.ui.fragment  FragmentStatisticsBinding $com.weihai.remotecontrol.ui.fragment  GridLayoutManager $com.weihai.remotecontrol.ui.fragment  GroupStatisticAdapter $com.weihai.remotecontrol.ui.fragment  LinearLayoutManager $com.weihai.remotecontrol.ui.fragment  List $com.weihai.remotecontrol.ui.fragment  R $com.weihai.remotecontrol.ui.fragment  StatisticsFragment $com.weihai.remotecontrol.ui.fragment  String $com.weihai.remotecontrol.ui.fragment  View $com.weihai.remotecontrol.ui.fragment  activityViewModels $com.weihai.remotecontrol.ui.fragment  apply $com.weihai.remotecontrol.ui.fragment  
deviceAdapter $com.weihai.remotecontrol.ui.fragment  forEach $com.weihai.remotecontrol.ui.fragment  getValue $com.weihai.remotecontrol.ui.fragment  groupStatisticAdapter $com.weihai.remotecontrol.ui.fragment  
isNotBlank $com.weihai.remotecontrol.ui.fragment  
isNotEmpty $com.weihai.remotecontrol.ui.fragment  provideDelegate $com.weihai.remotecontrol.ui.fragment  requireContext $com.weihai.remotecontrol.ui.fragment  Boolean 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  Bundle 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  Chip 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  Device 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  
DeviceAdapter 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  FragmentDevicesBinding 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  GridLayoutManager 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  LayoutInflater 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  List 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  
MainViewModel 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  R 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  String 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  View 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  	ViewGroup 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  _binding 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  activityViewModels 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  apply 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  binding 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  createGroupChip 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  currentGroup 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  
deviceAdapter 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  getACTIVITYViewModels 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  getAPPLY 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  getActivityViewModels 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  getApply 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  getGETValue 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  getGetValue 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  
getISNotBlank 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  
getISNotEmpty 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  
getIsNotBlank 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  
getIsNotEmpty 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  getPROVIDEDelegate 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  getProvideDelegate 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  	getString 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  getVIEWLifecycleOwner 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  getValue 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  getViewLifecycleOwner 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  
isNotBlank 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  
isNotEmpty 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  observeViewModel 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  provideDelegate 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  requireContext 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  setViewLifecycleOwner 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  setupGroupTabs 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  setupRecyclerView 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  updateDeviceList 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  updateEmptyState 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  updateGroupTabs 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  viewLifecycleOwner 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  	viewModel 4com.weihai.remotecontrol.ui.fragment.DevicesFragment  Bundle 7com.weihai.remotecontrol.ui.fragment.StatisticsFragment  FragmentStatisticsBinding 7com.weihai.remotecontrol.ui.fragment.StatisticsFragment  GroupStatisticAdapter 7com.weihai.remotecontrol.ui.fragment.StatisticsFragment  LayoutInflater 7com.weihai.remotecontrol.ui.fragment.StatisticsFragment  LinearLayoutManager 7com.weihai.remotecontrol.ui.fragment.StatisticsFragment  
MainViewModel 7com.weihai.remotecontrol.ui.fragment.StatisticsFragment  View 7com.weihai.remotecontrol.ui.fragment.StatisticsFragment  	ViewGroup 7com.weihai.remotecontrol.ui.fragment.StatisticsFragment  _binding 7com.weihai.remotecontrol.ui.fragment.StatisticsFragment  activityViewModels 7com.weihai.remotecontrol.ui.fragment.StatisticsFragment  apply 7com.weihai.remotecontrol.ui.fragment.StatisticsFragment  binding 7com.weihai.remotecontrol.ui.fragment.StatisticsFragment  getACTIVITYViewModels 7com.weihai.remotecontrol.ui.fragment.StatisticsFragment  getAPPLY 7com.weihai.remotecontrol.ui.fragment.StatisticsFragment  getActivityViewModels 7com.weihai.remotecontrol.ui.fragment.StatisticsFragment  getApply 7com.weihai.remotecontrol.ui.fragment.StatisticsFragment  getGETValue 7com.weihai.remotecontrol.ui.fragment.StatisticsFragment  getGetValue 7com.weihai.remotecontrol.ui.fragment.StatisticsFragment  getPROVIDEDelegate 7com.weihai.remotecontrol.ui.fragment.StatisticsFragment  getProvideDelegate 7com.weihai.remotecontrol.ui.fragment.StatisticsFragment  getVIEWLifecycleOwner 7com.weihai.remotecontrol.ui.fragment.StatisticsFragment  getValue 7com.weihai.remotecontrol.ui.fragment.StatisticsFragment  getViewLifecycleOwner 7com.weihai.remotecontrol.ui.fragment.StatisticsFragment  groupStatisticAdapter 7com.weihai.remotecontrol.ui.fragment.StatisticsFragment  observeViewModel 7com.weihai.remotecontrol.ui.fragment.StatisticsFragment  provideDelegate 7com.weihai.remotecontrol.ui.fragment.StatisticsFragment  requireContext 7com.weihai.remotecontrol.ui.fragment.StatisticsFragment  setViewLifecycleOwner 7com.weihai.remotecontrol.ui.fragment.StatisticsFragment  setupRecyclerView 7com.weihai.remotecontrol.ui.fragment.StatisticsFragment  updateGroupStatistics 7com.weihai.remotecontrol.ui.fragment.StatisticsFragment  viewLifecycleOwner 7com.weihai.remotecontrol.ui.fragment.StatisticsFragment  	viewModel 7com.weihai.remotecontrol.ui.fragment.StatisticsFragment  Boolean %com.weihai.remotecontrol.ui.viewmodel  DataRepository %com.weihai.remotecontrol.ui.viewmodel  
DeviceType %com.weihai.remotecontrol.ui.viewmodel  GroupStatistic %com.weihai.remotecontrol.ui.viewmodel  Int %com.weihai.remotecontrol.ui.viewmodel  List %com.weihai.remotecontrol.ui.viewmodel  
MainViewModel %com.weihai.remotecontrol.ui.viewmodel  MediatorLiveData %com.weihai.remotecontrol.ui.viewmodel  MutableLiveData %com.weihai.remotecontrol.ui.viewmodel  String %com.weihai.remotecontrol.ui.viewmodel  
_currentGroup %com.weihai.remotecontrol.ui.viewmodel  _devices %com.weihai.remotecontrol.ui.viewmodel  _searchQuery %com.weihai.remotecontrol.ui.viewmodel  apply %com.weihai.remotecontrol.ui.viewmodel  count %com.weihai.remotecontrol.ui.viewmodel  	emptyList %com.weihai.remotecontrol.ui.viewmodel  isBlank %com.weihai.remotecontrol.ui.viewmodel  map %com.weihai.remotecontrol.ui.viewmodel  updateFilteredDevices %com.weihai.remotecontrol.ui.viewmodel  Int 4com.weihai.remotecontrol.ui.viewmodel.GroupStatistic  String 4com.weihai.remotecontrol.ui.viewmodel.GroupStatistic  equals 4com.weihai.remotecontrol.ui.viewmodel.GroupStatistic  	groupName 4com.weihai.remotecontrol.ui.viewmodel.GroupStatistic  
indoorDevices 4com.weihai.remotecontrol.ui.viewmodel.GroupStatistic  standardDevices 4com.weihai.remotecontrol.ui.viewmodel.GroupStatistic  totalDevices 4com.weihai.remotecontrol.ui.viewmodel.GroupStatistic  Application 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  Boolean 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  DataRepository 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  Device 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  
DeviceType 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  GroupStatistic 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  Int 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  List 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  LiveData 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  MediatorLiveData 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  MutableLiveData 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  String 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  
_currentGroup 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  _devices 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  _groups 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  _searchQuery 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  _showAddDeviceDialog 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  _showAddGroupDialog 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  _showDeleteDeviceDialog 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  _showDeleteGroupDialog 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  _showEditDeviceDialog 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  _showEditGroupDialog 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  	addDevice 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  addGroup 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  apply 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  count 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  deleteDevice 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  deleteGroup 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  devices 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  
editDevice 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  	editGroup 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  	emptyList 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  filteredDevices 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  getAPPLY 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  getApply 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  getCOUNT 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  getCount 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  getEMPTYList 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  getEmptyList 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  getGroupDeviceCount 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  getGroupStatistics 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  
getISBlank 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  
getIsBlank 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  getMAP 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  getMap 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  groups 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  hideAddDeviceDialog 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  hideAddGroupDialog 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  hideDeleteDeviceDialog 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  hideDeleteGroupDialog 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  hideEditDeviceDialog 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  hideEditGroupDialog 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  
indoorDevices 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  isBlank 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  loadData 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  map 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  
repository 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  searchQuery 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  setCurrentGroup 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  setSearchQuery 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  showAddDeviceDialog 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  showAddGroupDialog 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  showDeleteDeviceDialog 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  showDeleteGroupDialog 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  showEditDeviceDialog 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  showEditGroupDialog 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  standardDevices 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  totalDevices 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  totalGroups 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  updateDevice 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  updateFilteredDevices 3com.weihai.remotecontrol.ui.viewmodel.MainViewModel  ActivityMainBinding 	java.lang  AddDeviceDialog 	java.lang  AddGroupDialog 	java.lang  AlertDialog 	java.lang  ArrayAdapter 	java.lang  Chip 	java.lang  ClipData 	java.lang  Context 	java.lang  
ContextCompat 	java.lang  DataRepository 	java.lang  DeleteConfirmDialog 	java.lang  Device 	java.lang  
DeviceAdapter 	java.lang  
DeviceType 	java.lang  DevicesFragment 	java.lang  DialogAddDeviceBinding 	java.lang  EditDeviceDialog 	java.lang  EditGroupDialog 	java.lang  EditText 	java.lang  FragmentDevicesBinding 	java.lang  FragmentStatisticsBinding 	java.lang  GridLayoutManager 	java.lang  GroupStatistic 	java.lang  GroupStatisticAdapter 	java.lang  Gson 	java.lang  IllegalArgumentException 	java.lang  ItemDeviceBinding 	java.lang  ItemGroupStatisticBinding 	java.lang  KEY_DEVICES 	java.lang  
KEY_GROUPS 	java.lang  LayoutInflater 	java.lang  LinearLayoutManager 	java.lang  MediatorLiveData 	java.lang  MutableLiveData 	java.lang  
PREFS_NAME 	java.lang  R 	java.lang  STANDARD 	java.lang  StatisticsFragment 	java.lang  System 	java.lang  Toast 	java.lang  View 	java.lang  
ViewHolder 	java.lang  
_currentGroup 	java.lang  _devices 	java.lang  _groups 	java.lang  _searchQuery 	java.lang  also 	java.lang  android 	java.lang  apply 	java.lang  contains 	java.lang  copyToClipboard 	java.lang  count 	java.lang  
deviceAdapter 	java.lang  	emptyList 	java.lang  filter 	java.lang  find 	java.lang  forEach 	java.lang  	getString 	java.lang  getValue 	java.lang  groupStatisticAdapter 	java.lang  gson 	java.lang  indexOfFirst 	java.lang  isBlank 	java.lang  
isNotBlank 	java.lang  
isNotEmpty 	java.lang  
isNullOrBlank 	java.lang  let 	java.lang  listOf 	java.lang  	lowercase 	java.lang  map 	java.lang  
mutableListOf 	java.lang  
onDeleteClick 	java.lang  onEditClick 	java.lang  originalName 	java.lang  provideDelegate 	java.lang  	removeAll 	java.lang  requireContext 	java.lang  synchronized 	java.lang  toList 	java.lang  
toMutableList 	java.lang  toString 	java.lang  trim 	java.lang  updateFilteredDevices 	java.lang  values 	java.lang  	viewModel 	java.lang  currentTimeMillis java.lang.System  Type java.lang.reflect  ActivityMainBinding kotlin  AddDeviceDialog kotlin  AddGroupDialog kotlin  AlertDialog kotlin  Any kotlin  Array kotlin  ArrayAdapter kotlin  Boolean kotlin  CharSequence kotlin  Chip kotlin  ClipData kotlin  Context kotlin  
ContextCompat kotlin  DataRepository kotlin  DeleteConfirmDialog kotlin  Device kotlin  
DeviceAdapter kotlin  
DeviceType kotlin  DevicesFragment kotlin  DialogAddDeviceBinding kotlin  EditDeviceDialog kotlin  EditGroupDialog kotlin  EditText kotlin  FragmentDevicesBinding kotlin  FragmentStatisticsBinding kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  GridLayoutManager kotlin  GroupStatistic kotlin  GroupStatisticAdapter kotlin  Gson kotlin  IllegalArgumentException kotlin  Int kotlin  ItemDeviceBinding kotlin  ItemGroupStatisticBinding kotlin  KEY_DEVICES kotlin  
KEY_GROUPS kotlin  LayoutInflater kotlin  Lazy kotlin  LinearLayoutManager kotlin  MediatorLiveData kotlin  MutableLiveData kotlin  Nothing kotlin  
PREFS_NAME kotlin  R kotlin  STANDARD kotlin  StatisticsFragment kotlin  String kotlin  System kotlin  Toast kotlin  Unit kotlin  View kotlin  
ViewHolder kotlin  Volatile kotlin  
_currentGroup kotlin  _devices kotlin  _groups kotlin  _searchQuery kotlin  also kotlin  android kotlin  apply kotlin  contains kotlin  copyToClipboard kotlin  count kotlin  
deviceAdapter kotlin  	emptyList kotlin  filter kotlin  find kotlin  forEach kotlin  	getString kotlin  getValue kotlin  groupStatisticAdapter kotlin  gson kotlin  indexOfFirst kotlin  isBlank kotlin  
isNotBlank kotlin  
isNotEmpty kotlin  
isNullOrBlank kotlin  let kotlin  listOf kotlin  	lowercase kotlin  map kotlin  
mutableListOf kotlin  
onDeleteClick kotlin  onEditClick kotlin  originalName kotlin  provideDelegate kotlin  	removeAll kotlin  requireContext kotlin  synchronized kotlin  toList kotlin  
toMutableList kotlin  toString kotlin  trim kotlin  updateFilteredDevices kotlin  values kotlin  	viewModel kotlin  getFIND kotlin.Array  getFind kotlin.Array  getFIND kotlin.Enum.Companion  getFind kotlin.Enum.Companion  getGETValue kotlin.Lazy  getGetValue kotlin.Lazy  getPROVIDEDelegate kotlin.Lazy  getProvideDelegate kotlin.Lazy  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  getCONTAINS 
kotlin.String  getContains 
kotlin.String  
getISBlank 
kotlin.String  
getISNotBlank 
kotlin.String  
getIsBlank 
kotlin.String  
getIsNotBlank 
kotlin.String  getLET 
kotlin.String  getLOWERCASE 
kotlin.String  getLet 
kotlin.String  getLowercase 
kotlin.String  getTRIM 
kotlin.String  getTrim 
kotlin.String  isBlank 
kotlin.String  
isNotBlank 
kotlin.String  ActivityMainBinding kotlin.annotation  AddDeviceDialog kotlin.annotation  AddGroupDialog kotlin.annotation  AlertDialog kotlin.annotation  ArrayAdapter kotlin.annotation  Chip kotlin.annotation  ClipData kotlin.annotation  Context kotlin.annotation  
ContextCompat kotlin.annotation  DataRepository kotlin.annotation  DeleteConfirmDialog kotlin.annotation  Device kotlin.annotation  
DeviceAdapter kotlin.annotation  
DeviceType kotlin.annotation  DevicesFragment kotlin.annotation  DialogAddDeviceBinding kotlin.annotation  EditDeviceDialog kotlin.annotation  EditGroupDialog kotlin.annotation  EditText kotlin.annotation  FragmentDevicesBinding kotlin.annotation  FragmentStatisticsBinding kotlin.annotation  GridLayoutManager kotlin.annotation  GroupStatistic kotlin.annotation  GroupStatisticAdapter kotlin.annotation  Gson kotlin.annotation  IllegalArgumentException kotlin.annotation  ItemDeviceBinding kotlin.annotation  ItemGroupStatisticBinding kotlin.annotation  KEY_DEVICES kotlin.annotation  
KEY_GROUPS kotlin.annotation  LayoutInflater kotlin.annotation  LinearLayoutManager kotlin.annotation  MediatorLiveData kotlin.annotation  MutableLiveData kotlin.annotation  
PREFS_NAME kotlin.annotation  R kotlin.annotation  STANDARD kotlin.annotation  StatisticsFragment kotlin.annotation  System kotlin.annotation  Toast kotlin.annotation  View kotlin.annotation  
ViewHolder kotlin.annotation  Volatile kotlin.annotation  
_currentGroup kotlin.annotation  _devices kotlin.annotation  _groups kotlin.annotation  _searchQuery kotlin.annotation  also kotlin.annotation  android kotlin.annotation  apply kotlin.annotation  contains kotlin.annotation  copyToClipboard kotlin.annotation  count kotlin.annotation  
deviceAdapter kotlin.annotation  	emptyList kotlin.annotation  filter kotlin.annotation  find kotlin.annotation  forEach kotlin.annotation  	getString kotlin.annotation  getValue kotlin.annotation  groupStatisticAdapter kotlin.annotation  gson kotlin.annotation  indexOfFirst kotlin.annotation  isBlank kotlin.annotation  
isNotBlank kotlin.annotation  
isNotEmpty kotlin.annotation  
isNullOrBlank kotlin.annotation  let kotlin.annotation  listOf kotlin.annotation  	lowercase kotlin.annotation  map kotlin.annotation  
mutableListOf kotlin.annotation  
onDeleteClick kotlin.annotation  onEditClick kotlin.annotation  originalName kotlin.annotation  provideDelegate kotlin.annotation  	removeAll kotlin.annotation  requireContext kotlin.annotation  synchronized kotlin.annotation  toList kotlin.annotation  
toMutableList kotlin.annotation  toString kotlin.annotation  trim kotlin.annotation  updateFilteredDevices kotlin.annotation  values kotlin.annotation  	viewModel kotlin.annotation  ActivityMainBinding kotlin.collections  AddDeviceDialog kotlin.collections  AddGroupDialog kotlin.collections  AlertDialog kotlin.collections  ArrayAdapter kotlin.collections  Chip kotlin.collections  ClipData kotlin.collections  Context kotlin.collections  
ContextCompat kotlin.collections  DataRepository kotlin.collections  DeleteConfirmDialog kotlin.collections  Device kotlin.collections  
DeviceAdapter kotlin.collections  
DeviceType kotlin.collections  DevicesFragment kotlin.collections  DialogAddDeviceBinding kotlin.collections  EditDeviceDialog kotlin.collections  EditGroupDialog kotlin.collections  EditText kotlin.collections  FragmentDevicesBinding kotlin.collections  FragmentStatisticsBinding kotlin.collections  GridLayoutManager kotlin.collections  GroupStatistic kotlin.collections  GroupStatisticAdapter kotlin.collections  Gson kotlin.collections  IllegalArgumentException kotlin.collections  ItemDeviceBinding kotlin.collections  ItemGroupStatisticBinding kotlin.collections  KEY_DEVICES kotlin.collections  
KEY_GROUPS kotlin.collections  LayoutInflater kotlin.collections  LinearLayoutManager kotlin.collections  List kotlin.collections  MediatorLiveData kotlin.collections  MutableList kotlin.collections  MutableLiveData kotlin.collections  
PREFS_NAME kotlin.collections  R kotlin.collections  STANDARD kotlin.collections  StatisticsFragment kotlin.collections  System kotlin.collections  Toast kotlin.collections  View kotlin.collections  
ViewHolder kotlin.collections  Volatile kotlin.collections  
_currentGroup kotlin.collections  _devices kotlin.collections  _groups kotlin.collections  _searchQuery kotlin.collections  also kotlin.collections  android kotlin.collections  apply kotlin.collections  contains kotlin.collections  copyToClipboard kotlin.collections  count kotlin.collections  
deviceAdapter kotlin.collections  	emptyList kotlin.collections  filter kotlin.collections  find kotlin.collections  forEach kotlin.collections  	getString kotlin.collections  getValue kotlin.collections  groupStatisticAdapter kotlin.collections  gson kotlin.collections  indexOfFirst kotlin.collections  isBlank kotlin.collections  
isNotBlank kotlin.collections  
isNotEmpty kotlin.collections  
isNullOrBlank kotlin.collections  let kotlin.collections  listOf kotlin.collections  	lowercase kotlin.collections  map kotlin.collections  
mutableListOf kotlin.collections  
onDeleteClick kotlin.collections  onEditClick kotlin.collections  originalName kotlin.collections  provideDelegate kotlin.collections  	removeAll kotlin.collections  requireContext kotlin.collections  synchronized kotlin.collections  toList kotlin.collections  
toMutableList kotlin.collections  toString kotlin.collections  trim kotlin.collections  updateFilteredDevices kotlin.collections  values kotlin.collections  	viewModel kotlin.collections  getCOUNT kotlin.collections.List  getCount kotlin.collections.List  	getFILTER kotlin.collections.List  	getFilter kotlin.collections.List  
getISNotEmpty kotlin.collections.List  
getIsNotEmpty kotlin.collections.List  getMAP kotlin.collections.List  getMap kotlin.collections.List  getTOMutableList kotlin.collections.List  getToMutableList kotlin.collections.List  
isNotEmpty kotlin.collections.List  getCOUNT kotlin.collections.MutableList  getCount kotlin.collections.MutableList  	getFILTER kotlin.collections.MutableList  getFIND kotlin.collections.MutableList  	getFilter kotlin.collections.MutableList  getFind kotlin.collections.MutableList  getINDEXOfFirst kotlin.collections.MutableList  
getISNotEmpty kotlin.collections.MutableList  getIndexOfFirst kotlin.collections.MutableList  
getIsNotEmpty kotlin.collections.MutableList  getREMOVEAll kotlin.collections.MutableList  getRemoveAll kotlin.collections.MutableList  	getTOList kotlin.collections.MutableList  	getToList kotlin.collections.MutableList  
isNotEmpty kotlin.collections.MutableList  ActivityMainBinding kotlin.comparisons  AddDeviceDialog kotlin.comparisons  AddGroupDialog kotlin.comparisons  AlertDialog kotlin.comparisons  ArrayAdapter kotlin.comparisons  Chip kotlin.comparisons  ClipData kotlin.comparisons  Context kotlin.comparisons  
ContextCompat kotlin.comparisons  DataRepository kotlin.comparisons  DeleteConfirmDialog kotlin.comparisons  Device kotlin.comparisons  
DeviceAdapter kotlin.comparisons  
DeviceType kotlin.comparisons  DevicesFragment kotlin.comparisons  DialogAddDeviceBinding kotlin.comparisons  EditDeviceDialog kotlin.comparisons  EditGroupDialog kotlin.comparisons  EditText kotlin.comparisons  FragmentDevicesBinding kotlin.comparisons  FragmentStatisticsBinding kotlin.comparisons  GridLayoutManager kotlin.comparisons  GroupStatistic kotlin.comparisons  GroupStatisticAdapter kotlin.comparisons  Gson kotlin.comparisons  IllegalArgumentException kotlin.comparisons  ItemDeviceBinding kotlin.comparisons  ItemGroupStatisticBinding kotlin.comparisons  KEY_DEVICES kotlin.comparisons  
KEY_GROUPS kotlin.comparisons  LayoutInflater kotlin.comparisons  LinearLayoutManager kotlin.comparisons  MediatorLiveData kotlin.comparisons  MutableLiveData kotlin.comparisons  
PREFS_NAME kotlin.comparisons  R kotlin.comparisons  STANDARD kotlin.comparisons  StatisticsFragment kotlin.comparisons  System kotlin.comparisons  Toast kotlin.comparisons  View kotlin.comparisons  
ViewHolder kotlin.comparisons  Volatile kotlin.comparisons  
_currentGroup kotlin.comparisons  _devices kotlin.comparisons  _groups kotlin.comparisons  _searchQuery kotlin.comparisons  also kotlin.comparisons  android kotlin.comparisons  apply kotlin.comparisons  contains kotlin.comparisons  copyToClipboard kotlin.comparisons  count kotlin.comparisons  
deviceAdapter kotlin.comparisons  	emptyList kotlin.comparisons  filter kotlin.comparisons  find kotlin.comparisons  forEach kotlin.comparisons  	getString kotlin.comparisons  getValue kotlin.comparisons  groupStatisticAdapter kotlin.comparisons  gson kotlin.comparisons  indexOfFirst kotlin.comparisons  isBlank kotlin.comparisons  
isNotBlank kotlin.comparisons  
isNotEmpty kotlin.comparisons  
isNullOrBlank kotlin.comparisons  let kotlin.comparisons  listOf kotlin.comparisons  	lowercase kotlin.comparisons  map kotlin.comparisons  
mutableListOf kotlin.comparisons  
onDeleteClick kotlin.comparisons  onEditClick kotlin.comparisons  originalName kotlin.comparisons  provideDelegate kotlin.comparisons  	removeAll kotlin.comparisons  requireContext kotlin.comparisons  synchronized kotlin.comparisons  toList kotlin.comparisons  
toMutableList kotlin.comparisons  toString kotlin.comparisons  trim kotlin.comparisons  updateFilteredDevices kotlin.comparisons  values kotlin.comparisons  	viewModel kotlin.comparisons  ActivityMainBinding 	kotlin.io  AddDeviceDialog 	kotlin.io  AddGroupDialog 	kotlin.io  AlertDialog 	kotlin.io  ArrayAdapter 	kotlin.io  Chip 	kotlin.io  ClipData 	kotlin.io  Context 	kotlin.io  
ContextCompat 	kotlin.io  DataRepository 	kotlin.io  DeleteConfirmDialog 	kotlin.io  Device 	kotlin.io  
DeviceAdapter 	kotlin.io  
DeviceType 	kotlin.io  DevicesFragment 	kotlin.io  DialogAddDeviceBinding 	kotlin.io  EditDeviceDialog 	kotlin.io  EditGroupDialog 	kotlin.io  EditText 	kotlin.io  FragmentDevicesBinding 	kotlin.io  FragmentStatisticsBinding 	kotlin.io  GridLayoutManager 	kotlin.io  GroupStatistic 	kotlin.io  GroupStatisticAdapter 	kotlin.io  Gson 	kotlin.io  IllegalArgumentException 	kotlin.io  ItemDeviceBinding 	kotlin.io  ItemGroupStatisticBinding 	kotlin.io  KEY_DEVICES 	kotlin.io  
KEY_GROUPS 	kotlin.io  LayoutInflater 	kotlin.io  LinearLayoutManager 	kotlin.io  MediatorLiveData 	kotlin.io  MutableLiveData 	kotlin.io  
PREFS_NAME 	kotlin.io  R 	kotlin.io  STANDARD 	kotlin.io  StatisticsFragment 	kotlin.io  System 	kotlin.io  Toast 	kotlin.io  View 	kotlin.io  
ViewHolder 	kotlin.io  Volatile 	kotlin.io  
_currentGroup 	kotlin.io  _devices 	kotlin.io  _groups 	kotlin.io  _searchQuery 	kotlin.io  also 	kotlin.io  android 	kotlin.io  apply 	kotlin.io  contains 	kotlin.io  copyToClipboard 	kotlin.io  count 	kotlin.io  
deviceAdapter 	kotlin.io  	emptyList 	kotlin.io  filter 	kotlin.io  find 	kotlin.io  forEach 	kotlin.io  	getString 	kotlin.io  getValue 	kotlin.io  groupStatisticAdapter 	kotlin.io  gson 	kotlin.io  indexOfFirst 	kotlin.io  isBlank 	kotlin.io  
isNotBlank 	kotlin.io  
isNotEmpty 	kotlin.io  
isNullOrBlank 	kotlin.io  let 	kotlin.io  listOf 	kotlin.io  	lowercase 	kotlin.io  map 	kotlin.io  
mutableListOf 	kotlin.io  
onDeleteClick 	kotlin.io  onEditClick 	kotlin.io  originalName 	kotlin.io  provideDelegate 	kotlin.io  	removeAll 	kotlin.io  requireContext 	kotlin.io  synchronized 	kotlin.io  toList 	kotlin.io  
toMutableList 	kotlin.io  toString 	kotlin.io  trim 	kotlin.io  updateFilteredDevices 	kotlin.io  values 	kotlin.io  	viewModel 	kotlin.io  ActivityMainBinding 
kotlin.jvm  AddDeviceDialog 
kotlin.jvm  AddGroupDialog 
kotlin.jvm  AlertDialog 
kotlin.jvm  ArrayAdapter 
kotlin.jvm  Chip 
kotlin.jvm  ClipData 
kotlin.jvm  Context 
kotlin.jvm  
ContextCompat 
kotlin.jvm  DataRepository 
kotlin.jvm  DeleteConfirmDialog 
kotlin.jvm  Device 
kotlin.jvm  
DeviceAdapter 
kotlin.jvm  
DeviceType 
kotlin.jvm  DevicesFragment 
kotlin.jvm  DialogAddDeviceBinding 
kotlin.jvm  EditDeviceDialog 
kotlin.jvm  EditGroupDialog 
kotlin.jvm  EditText 
kotlin.jvm  FragmentDevicesBinding 
kotlin.jvm  FragmentStatisticsBinding 
kotlin.jvm  GridLayoutManager 
kotlin.jvm  GroupStatistic 
kotlin.jvm  GroupStatisticAdapter 
kotlin.jvm  Gson 
kotlin.jvm  IllegalArgumentException 
kotlin.jvm  ItemDeviceBinding 
kotlin.jvm  ItemGroupStatisticBinding 
kotlin.jvm  KEY_DEVICES 
kotlin.jvm  
KEY_GROUPS 
kotlin.jvm  LayoutInflater 
kotlin.jvm  LinearLayoutManager 
kotlin.jvm  MediatorLiveData 
kotlin.jvm  MutableLiveData 
kotlin.jvm  
PREFS_NAME 
kotlin.jvm  R 
kotlin.jvm  STANDARD 
kotlin.jvm  StatisticsFragment 
kotlin.jvm  System 
kotlin.jvm  Toast 
kotlin.jvm  View 
kotlin.jvm  
ViewHolder 
kotlin.jvm  Volatile 
kotlin.jvm  
_currentGroup 
kotlin.jvm  _devices 
kotlin.jvm  _groups 
kotlin.jvm  _searchQuery 
kotlin.jvm  also 
kotlin.jvm  android 
kotlin.jvm  apply 
kotlin.jvm  contains 
kotlin.jvm  copyToClipboard 
kotlin.jvm  count 
kotlin.jvm  
deviceAdapter 
kotlin.jvm  	emptyList 
kotlin.jvm  filter 
kotlin.jvm  find 
kotlin.jvm  forEach 
kotlin.jvm  	getString 
kotlin.jvm  getValue 
kotlin.jvm  groupStatisticAdapter 
kotlin.jvm  gson 
kotlin.jvm  indexOfFirst 
kotlin.jvm  isBlank 
kotlin.jvm  
isNotBlank 
kotlin.jvm  
isNotEmpty 
kotlin.jvm  
isNullOrBlank 
kotlin.jvm  let 
kotlin.jvm  listOf 
kotlin.jvm  	lowercase 
kotlin.jvm  map 
kotlin.jvm  
mutableListOf 
kotlin.jvm  
onDeleteClick 
kotlin.jvm  onEditClick 
kotlin.jvm  originalName 
kotlin.jvm  provideDelegate 
kotlin.jvm  	removeAll 
kotlin.jvm  requireContext 
kotlin.jvm  synchronized 
kotlin.jvm  toList 
kotlin.jvm  
toMutableList 
kotlin.jvm  toString 
kotlin.jvm  trim 
kotlin.jvm  updateFilteredDevices 
kotlin.jvm  values 
kotlin.jvm  	viewModel 
kotlin.jvm  ActivityMainBinding 
kotlin.ranges  AddDeviceDialog 
kotlin.ranges  AddGroupDialog 
kotlin.ranges  AlertDialog 
kotlin.ranges  ArrayAdapter 
kotlin.ranges  Chip 
kotlin.ranges  ClipData 
kotlin.ranges  Context 
kotlin.ranges  
ContextCompat 
kotlin.ranges  DataRepository 
kotlin.ranges  DeleteConfirmDialog 
kotlin.ranges  Device 
kotlin.ranges  
DeviceAdapter 
kotlin.ranges  
DeviceType 
kotlin.ranges  DevicesFragment 
kotlin.ranges  DialogAddDeviceBinding 
kotlin.ranges  EditDeviceDialog 
kotlin.ranges  EditGroupDialog 
kotlin.ranges  EditText 
kotlin.ranges  FragmentDevicesBinding 
kotlin.ranges  FragmentStatisticsBinding 
kotlin.ranges  GridLayoutManager 
kotlin.ranges  GroupStatistic 
kotlin.ranges  GroupStatisticAdapter 
kotlin.ranges  Gson 
kotlin.ranges  IllegalArgumentException 
kotlin.ranges  ItemDeviceBinding 
kotlin.ranges  ItemGroupStatisticBinding 
kotlin.ranges  KEY_DEVICES 
kotlin.ranges  
KEY_GROUPS 
kotlin.ranges  LayoutInflater 
kotlin.ranges  LinearLayoutManager 
kotlin.ranges  MediatorLiveData 
kotlin.ranges  MutableLiveData 
kotlin.ranges  
PREFS_NAME 
kotlin.ranges  R 
kotlin.ranges  STANDARD 
kotlin.ranges  StatisticsFragment 
kotlin.ranges  System 
kotlin.ranges  Toast 
kotlin.ranges  View 
kotlin.ranges  
ViewHolder 
kotlin.ranges  Volatile 
kotlin.ranges  
_currentGroup 
kotlin.ranges  _devices 
kotlin.ranges  _groups 
kotlin.ranges  _searchQuery 
kotlin.ranges  also 
kotlin.ranges  android 
kotlin.ranges  apply 
kotlin.ranges  contains 
kotlin.ranges  copyToClipboard 
kotlin.ranges  count 
kotlin.ranges  
deviceAdapter 
kotlin.ranges  	emptyList 
kotlin.ranges  filter 
kotlin.ranges  find 
kotlin.ranges  forEach 
kotlin.ranges  	getString 
kotlin.ranges  getValue 
kotlin.ranges  groupStatisticAdapter 
kotlin.ranges  gson 
kotlin.ranges  indexOfFirst 
kotlin.ranges  isBlank 
kotlin.ranges  
isNotBlank 
kotlin.ranges  
isNotEmpty 
kotlin.ranges  
isNullOrBlank 
kotlin.ranges  let 
kotlin.ranges  listOf 
kotlin.ranges  	lowercase 
kotlin.ranges  map 
kotlin.ranges  
mutableListOf 
kotlin.ranges  
onDeleteClick 
kotlin.ranges  onEditClick 
kotlin.ranges  originalName 
kotlin.ranges  provideDelegate 
kotlin.ranges  	removeAll 
kotlin.ranges  requireContext 
kotlin.ranges  synchronized 
kotlin.ranges  toList 
kotlin.ranges  
toMutableList 
kotlin.ranges  toString 
kotlin.ranges  trim 
kotlin.ranges  updateFilteredDevices 
kotlin.ranges  values 
kotlin.ranges  	viewModel 
kotlin.ranges  ActivityMainBinding kotlin.sequences  AddDeviceDialog kotlin.sequences  AddGroupDialog kotlin.sequences  AlertDialog kotlin.sequences  ArrayAdapter kotlin.sequences  Chip kotlin.sequences  ClipData kotlin.sequences  Context kotlin.sequences  
ContextCompat kotlin.sequences  DataRepository kotlin.sequences  DeleteConfirmDialog kotlin.sequences  Device kotlin.sequences  
DeviceAdapter kotlin.sequences  
DeviceType kotlin.sequences  DevicesFragment kotlin.sequences  DialogAddDeviceBinding kotlin.sequences  EditDeviceDialog kotlin.sequences  EditGroupDialog kotlin.sequences  EditText kotlin.sequences  FragmentDevicesBinding kotlin.sequences  FragmentStatisticsBinding kotlin.sequences  GridLayoutManager kotlin.sequences  GroupStatistic kotlin.sequences  GroupStatisticAdapter kotlin.sequences  Gson kotlin.sequences  IllegalArgumentException kotlin.sequences  ItemDeviceBinding kotlin.sequences  ItemGroupStatisticBinding kotlin.sequences  KEY_DEVICES kotlin.sequences  
KEY_GROUPS kotlin.sequences  LayoutInflater kotlin.sequences  LinearLayoutManager kotlin.sequences  MediatorLiveData kotlin.sequences  MutableLiveData kotlin.sequences  
PREFS_NAME kotlin.sequences  R kotlin.sequences  STANDARD kotlin.sequences  StatisticsFragment kotlin.sequences  System kotlin.sequences  Toast kotlin.sequences  View kotlin.sequences  
ViewHolder kotlin.sequences  Volatile kotlin.sequences  
_currentGroup kotlin.sequences  _devices kotlin.sequences  _groups kotlin.sequences  _searchQuery kotlin.sequences  also kotlin.sequences  android kotlin.sequences  apply kotlin.sequences  contains kotlin.sequences  copyToClipboard kotlin.sequences  count kotlin.sequences  
deviceAdapter kotlin.sequences  	emptyList kotlin.sequences  filter kotlin.sequences  find kotlin.sequences  forEach kotlin.sequences  	getString kotlin.sequences  getValue kotlin.sequences  groupStatisticAdapter kotlin.sequences  gson kotlin.sequences  indexOfFirst kotlin.sequences  isBlank kotlin.sequences  
isNotBlank kotlin.sequences  
isNotEmpty kotlin.sequences  
isNullOrBlank kotlin.sequences  let kotlin.sequences  listOf kotlin.sequences  	lowercase kotlin.sequences  map kotlin.sequences  
mutableListOf kotlin.sequences  
onDeleteClick kotlin.sequences  onEditClick kotlin.sequences  originalName kotlin.sequences  provideDelegate kotlin.sequences  	removeAll kotlin.sequences  requireContext kotlin.sequences  synchronized kotlin.sequences  toList kotlin.sequences  
toMutableList kotlin.sequences  toString kotlin.sequences  trim kotlin.sequences  updateFilteredDevices kotlin.sequences  values kotlin.sequences  	viewModel kotlin.sequences  ActivityMainBinding kotlin.text  AddDeviceDialog kotlin.text  AddGroupDialog kotlin.text  AlertDialog kotlin.text  ArrayAdapter kotlin.text  Chip kotlin.text  ClipData kotlin.text  Context kotlin.text  
ContextCompat kotlin.text  DataRepository kotlin.text  DeleteConfirmDialog kotlin.text  Device kotlin.text  
DeviceAdapter kotlin.text  
DeviceType kotlin.text  DevicesFragment kotlin.text  DialogAddDeviceBinding kotlin.text  EditDeviceDialog kotlin.text  EditGroupDialog kotlin.text  EditText kotlin.text  FragmentDevicesBinding kotlin.text  FragmentStatisticsBinding kotlin.text  GridLayoutManager kotlin.text  GroupStatistic kotlin.text  GroupStatisticAdapter kotlin.text  Gson kotlin.text  IllegalArgumentException kotlin.text  ItemDeviceBinding kotlin.text  ItemGroupStatisticBinding kotlin.text  KEY_DEVICES kotlin.text  
KEY_GROUPS kotlin.text  LayoutInflater kotlin.text  LinearLayoutManager kotlin.text  MediatorLiveData kotlin.text  MutableLiveData kotlin.text  
PREFS_NAME kotlin.text  R kotlin.text  STANDARD kotlin.text  StatisticsFragment kotlin.text  System kotlin.text  Toast kotlin.text  View kotlin.text  
ViewHolder kotlin.text  Volatile kotlin.text  
_currentGroup kotlin.text  _devices kotlin.text  _groups kotlin.text  _searchQuery kotlin.text  also kotlin.text  android kotlin.text  apply kotlin.text  contains kotlin.text  copyToClipboard kotlin.text  count kotlin.text  
deviceAdapter kotlin.text  	emptyList kotlin.text  filter kotlin.text  find kotlin.text  forEach kotlin.text  	getString kotlin.text  getValue kotlin.text  groupStatisticAdapter kotlin.text  gson kotlin.text  indexOfFirst kotlin.text  isBlank kotlin.text  
isNotBlank kotlin.text  
isNotEmpty kotlin.text  
isNullOrBlank kotlin.text  let kotlin.text  listOf kotlin.text  	lowercase kotlin.text  map kotlin.text  
mutableListOf kotlin.text  
onDeleteClick kotlin.text  onEditClick kotlin.text  originalName kotlin.text  provideDelegate kotlin.text  	removeAll kotlin.text  requireContext kotlin.text  synchronized kotlin.text  toList kotlin.text  
toMutableList kotlin.text  toString kotlin.text  trim kotlin.text  updateFilteredDevices kotlin.text  values kotlin.text  	viewModel kotlin.text  LinearLayoutManager 4com.weihai.remotecontrol.ui.fragment.DevicesFragment                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             