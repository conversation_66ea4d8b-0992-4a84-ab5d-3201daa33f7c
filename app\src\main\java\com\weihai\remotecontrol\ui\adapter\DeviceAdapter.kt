package com.weihai.remotecontrol.ui.adapter

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.weihai.remotecontrol.R
import com.weihai.remotecontrol.data.Device
import com.weihai.remotecontrol.data.DeviceType
import com.weihai.remotecontrol.databinding.ItemDeviceBinding

/**
 * 设备列表适配器
 */
class DeviceAdapter(
    private val onEditClick: (Device) -> Unit,
    private val onDeleteClick: (Device) -> Unit
) : ListAdapter<Device, DeviceAdapter.DeviceViewHolder>(DeviceDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DeviceViewHolder {
        val binding = ItemDeviceBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return DeviceViewHolder(binding)
    }

    override fun onBindViewHolder(holder: DeviceViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class DeviceViewHolder(
        private val binding: ItemDeviceBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(device: Device) {
            binding.apply {
                // 设置设备信息
                tvDeviceName.text = device.name
                tvDeviceCode.text = device.code
                tvDevicePassword.text = device.password
                
                // 设置分组标签
                chipGroup.text = device.group
                
                // 设置设备类型标签
                chipType.text = device.type.displayName
                when (device.type) {
                    DeviceType.INDOOR -> {
                        chipType.setChipBackgroundColorResource(R.color.indoor_bg)
                        chipType.setTextColor(ContextCompat.getColor(root.context, R.color.indoor_text))
                    }
                    DeviceType.STANDARD -> {
                        chipType.setChipBackgroundColorResource(R.color.standard_bg)
                        chipType.setTextColor(ContextCompat.getColor(root.context, R.color.standard_text))
                    }
                }
                
                // 复制按钮点击事件
                btnCopyCode.setOnClickListener {
                    copyToClipboard(root.context, device.code, "连接码")
                }
                
                btnCopyPassword.setOnClickListener {
                    copyToClipboard(root.context, device.password, "连接密码")
                }
                
                // 编辑和删除按钮点击事件
                btnEditDevice.setOnClickListener {
                    onEditClick(device)
                }
                
                btnDeleteDevice.setOnClickListener {
                    onDeleteClick(device)
                }
            }
        }
        
        private fun copyToClipboard(context: Context, text: String, label: String) {
            val clipboard = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            val clip = ClipData.newPlainText(label, text)
            clipboard.setPrimaryClip(clip)
            Toast.makeText(context, context.getString(R.string.copy_success), Toast.LENGTH_SHORT).show()
        }
    }
}

/**
 * 设备差异比较器
 */
class DeviceDiffCallback : DiffUtil.ItemCallback<Device>() {
    override fun areItemsTheSame(oldItem: Device, newItem: Device): Boolean {
        return oldItem.id == newItem.id
    }

    override fun areContentsTheSame(oldItem: Device, newItem: Device): Boolean {
        return oldItem == newItem
    }
}
