<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 标题 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="@string/data_statistics"
            android:textColor="@color/dark"
            android:textSize="24sp"
            android:textStyle="bold" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            android:text="@string/statistics_description"
            android:textColor="@color/gray_500"
            android:textSize="16sp" />

        <!-- 统计卡片网格 -->
        <GridLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="32dp"
            android:columnCount="2"
            android:rowCount="2">

            <!-- 总设备数 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="0dp"
                android:layout_height="120dp"
                android:layout_rowWeight="1"
                android:layout_columnWeight="1"
                android:layout_margin="8dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="@string/total_devices"
                        android:textColor="@color/gray_500"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tvTotalDevices"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/primary"
                        android:textSize="32sp"
                        android:textStyle="bold"
                        tools:text="26" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 分组数量 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="0dp"
                android:layout_height="120dp"
                android:layout_rowWeight="1"
                android:layout_columnWeight="1"
                android:layout_margin="8dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="@string/total_groups"
                        android:textColor="@color/gray_500"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tvTotalGroups"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/secondary"
                        android:textSize="32sp"
                        android:textStyle="bold"
                        tools:text="6" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 室内设备 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="0dp"
                android:layout_height="120dp"
                android:layout_rowWeight="1"
                android:layout_columnWeight="1"
                android:layout_margin="8dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="@string/indoor_devices"
                        android:textColor="@color/gray_500"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tvIndoorDevices"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/indoor_text"
                        android:textSize="32sp"
                        android:textStyle="bold"
                        tools:text="0" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 标准设备 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="0dp"
                android:layout_height="120dp"
                android:layout_rowWeight="1"
                android:layout_columnWeight="1"
                android:layout_margin="8dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="@string/standard_devices"
                        android:textColor="@color/gray_500"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tvStandardDevices"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/standard_text"
                        android:textSize="32sp"
                        android:textStyle="bold"
                        tools:text="26" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </GridLayout>

        <!-- 分组详细统计 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/gray_50"
                    android:padding="16dp"
                    android:text="@string/group_detail_statistics"
                    android:textColor="@color/dark"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerViewGroupStats"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="16dp" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

</ScrollView>
