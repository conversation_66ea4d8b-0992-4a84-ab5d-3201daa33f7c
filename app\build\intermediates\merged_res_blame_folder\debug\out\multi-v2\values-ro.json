{"logs": [{"outputFile": "com.weihai.remotecontrol.app-mergeDebugResources-47:/values-ro/values-ro.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1ae08651db4bbb07f10e1945a5f6d854\\transformed\\appcompat-1.6.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,330,443,527,631,752,837,917,1008,1101,1196,1290,1390,1483,1578,1672,1763,1855,1938,2050,2158,2258,2372,2478,2584,2748,2851", "endColumns": "120,103,112,83,103,120,84,79,90,92,94,93,99,92,94,93,90,91,82,111,107,99,113,105,105,163,102,83", "endOffsets": "221,325,438,522,626,747,832,912,1003,1096,1191,1285,1385,1478,1573,1667,1758,1850,1933,2045,2153,2253,2367,2473,2579,2743,2846,2930"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "371,492,596,709,793,897,1018,1103,1183,1274,1367,1462,1556,1656,1749,1844,1938,2029,2121,2204,2316,2424,2524,2638,2744,2850,3014,9676", "endColumns": "120,103,112,83,103,120,84,79,90,92,94,93,99,92,94,93,90,91,82,111,107,99,113,105,105,163,102,83", "endOffsets": "487,591,704,788,892,1013,1098,1178,1269,1362,1457,1551,1651,1744,1839,1933,2024,2116,2199,2311,2419,2519,2633,2739,2845,3009,3112,9755"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5490b645e9d6a61333b77d70f2a2a530\\transformed\\core-1.12.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,454,556,665,782", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "148,250,350,449,551,660,777,878"}, "to": {"startLines": "39,40,41,42,43,44,45,112", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3570,3668,3770,3870,3969,4071,4180,9760", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "3663,3765,3865,3964,4066,4175,4292,9856"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a9838fd12d930687e6917e413d05bcf9\\transformed\\material-1.11.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,321,413,501,588,684,774,875,996,1080,1146,1241,1315,1375,1459,1521,1587,1645,1718,1781,1837,1956,2013,2074,2130,2204,2349,2435,2519,2652,2734,2817,2963,3053,3133,3188,3239,3305,3378,3456,3544,3629,3700,3777,3851,3923,4029,4120,4194,4289,4387,4461,4541,4642,4695,4781,4847,4936,5026,5088,5152,5215,5289,5401,5511,5621,5726,5785,5840", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "endColumns": "12,91,87,86,95,89,100,120,83,65,94,73,59,83,61,65,57,72,62,55,118,56,60,55,73,144,85,83,132,81,82,145,89,79,54,50,65,72,77,87,84,70,76,73,71,105,90,73,94,97,73,79,100,52,85,65,88,89,61,63,62,73,111,109,109,104,58,54,78", "endOffsets": "316,408,496,583,679,769,870,991,1075,1141,1236,1310,1370,1454,1516,1582,1640,1713,1776,1832,1951,2008,2069,2125,2199,2344,2430,2514,2647,2729,2812,2958,3048,3128,3183,3234,3300,3373,3451,3539,3624,3695,3772,3846,3918,4024,4115,4189,4284,4382,4456,4536,4637,4690,4776,4842,4931,5021,5083,5147,5210,5284,5396,5506,5616,5721,5780,5835,5914"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3117,3209,3297,3384,3480,4297,4398,4519,4603,4669,4764,4838,4898,4982,5044,5110,5168,5241,5304,5360,5479,5536,5597,5653,5727,5872,5958,6042,6175,6257,6340,6486,6576,6656,6711,6762,6828,6901,6979,7067,7152,7223,7300,7374,7446,7552,7643,7717,7812,7910,7984,8064,8165,8218,8304,8370,8459,8549,8611,8675,8738,8812,8924,9034,9144,9249,9308,9597", "endLines": "6,34,35,36,37,38,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,110", "endColumns": "12,91,87,86,95,89,100,120,83,65,94,73,59,83,61,65,57,72,62,55,118,56,60,55,73,144,85,83,132,81,82,145,89,79,54,50,65,72,77,87,84,70,76,73,71,105,90,73,94,97,73,79,100,52,85,65,88,89,61,63,62,73,111,109,109,104,58,54,78", "endOffsets": "366,3204,3292,3379,3475,3565,4393,4514,4598,4664,4759,4833,4893,4977,5039,5105,5163,5236,5299,5355,5474,5531,5592,5648,5722,5867,5953,6037,6170,6252,6335,6481,6571,6651,6706,6757,6823,6896,6974,7062,7147,7218,7295,7369,7441,7547,7638,7712,7807,7905,7979,8059,8160,8213,8299,8365,8454,8544,8606,8670,8733,8807,8919,9029,9139,9244,9303,9358,9671"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\822ef5295c54b3fc6575f1874f116e7d\\transformed\\navigation-ui-2.7.6\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,123", "endOffsets": "160,284"}, "to": {"startLines": "108,109", "startColumns": "4,4", "startOffsets": "9363,9473", "endColumns": "109,123", "endOffsets": "9468,9592"}}]}]}